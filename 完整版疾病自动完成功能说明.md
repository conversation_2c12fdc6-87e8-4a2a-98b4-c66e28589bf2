# 完整版疾病自动完成功能说明

## 🎯 功能概述

完整版疾病自动完成组件集成了所有高级功能：
- ✅ **智能搜索** - 支持疾病名称、拼音、ICD编码搜索
- ✅ **使用频次统计** - 自动记录和显示使用频次
- ✅ **自动新增** - 新疾病自动添加到数据库
- ✅ **ICD10辅助选择** - 标准疾病编码选择
- ✅ **丰富的UI展示** - 显示使用频次、最后使用时间、ICD编码
- ✅ **降级处理** - API失败时使用本地数据

## 🎨 界面特性

### 下拉选项显示
```
┌─────────────────────────────────────────┐
│ 高血压                        I10       │
│                        使用50次  今天   │
├─────────────────────────────────────────┤
│ 糖尿病                        E11       │
│                        使用45次  昨天   │
├─────────────────────────────────────────┤
│ 冠心病                        I25       │
│                        使用40次  2天前  │
└─────────────────────────────────────────┘
```

### 组件布局
```
┌─────────────────────────────────┬─────────┐
│ 请输入疾病名称...               │ ICD10   │
└─────────────────────────────────┴─────────┘
```

## 🔧 使用方法

### 基本使用
```vue
<template>
  <DiseaseAutoComplete
    v-model:value="formData.disease"
    placeholder="请输入疾病名称"
    field-name="disease"
    :limit="10"
    :show-icd-button="true"
    :disabled="false"
    @change="handleDiseaseChange"
    @select="handleDiseaseSelect"
  />
</template>

<script setup>
import DiseaseAutoComplete from './DiseaseAutoComplete.vue';

const formData = reactive({
  disease: ''
});

function handleDiseaseChange(value) {
  console.log('疾病名称变化:', value);
}

function handleDiseaseSelect(value, option) {
  console.log('选择疾病:', value, option);
}
</script>
```

### 属性配置
| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 绑定值 |
| placeholder | String | '请输入疾病名称' | 占位符 |
| disabled | Boolean | false | 是否禁用 |
| fieldName | String | 'disease' | 字段名称 |
| limit | Number | 10 | 选项数量限制 |
| showIcdButton | Boolean | true | 是否显示ICD10按钮 |

### 事件
| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (value: string) | 值变化时触发 |
| select | (value: string, option: object) | 选择选项时触发 |
| update:value | (value: string) | v-model更新 |

## 🚀 核心功能

### 1. 智能搜索
- **中文搜索**: 输入"高血压"匹配相关疾病
- **拼音搜索**: 输入"gxy"匹配"高血压"
- **ICD搜索**: 输入"I10"匹配"高血压"
- **模糊匹配**: 支持部分匹配

### 2. 使用频次统计
- **自动记录**: 每次选择自动增加使用频次
- **智能排序**: 按使用频次降序排列
- **时间记录**: 记录最后使用时间
- **友好显示**: "使用50次"、"今天"、"2天前"

### 3. 自动新增功能
- **智能检测**: 自动判断是否为新疾病
- **无感添加**: 用户正常输入，系统后台处理
- **数据完整**: 自动生成拼音、设置分类
- **避免重复**: 智能去重机制

### 4. ICD10辅助选择
- **标准编码**: 支持ICD10国际疾病分类
- **弹窗选择**: 独立的选择界面
- **自动填充**: 选择后自动填充疾病名称
- **编码显示**: 在选项中显示ICD编码

## 📡 API交互

### 初始化请求
```
GET /system/questionnaireCommonOptions/popular
参数: fieldName=disease&limit=10
响应: 热门疾病列表
```

### 搜索请求
```
GET /system/questionnaireCommonOptions/search
参数: fieldName=disease&keyword=高&limit=10
响应: 匹配的疾病列表
```

### 新增请求
```
POST /system/questionnaireCommonOptions/add
数据: {
  "fieldName": "disease",
  "optionValue": "xinjibing",
  "optionText": "新疾病",
  "useCount": 1,
  "category": "疾病"
}
```

### 使用记录请求
```
POST /system/questionnaireCommonOptions/recordUsage
参数: fieldName=disease&optionValue=hypertension&optionText=高血压
```

## 🎭 用户交互场景

### 场景1: 首次打开
1. 组件挂载 → 加载热门选项
2. 点击输入框 → 显示热门疾病下拉
3. 选择疾病 → 记录使用频次

### 场景2: 搜索已有疾病
1. 输入"高" → 触发搜索
2. 显示匹配结果 → 高血压、高血脂等
3. 选择"高血压" → 更新使用频次

### 场景3: 输入新疾病
1. 输入"新型疾病" → 搜索无结果
2. 按回车确认 → 自动添加到数据库
3. 记录使用频次 → 初始频次为1

### 场景4: ICD10选择
1. 点击"ICD10"按钮 → 打开选择弹窗
2. 搜索"I10" → 显示高血压
3. 确认选择 → 自动填充疾病名称

## 🔍 调试信息

### Console日志示例
```
🚀 完整版DiseaseAutoComplete组件已挂载，开始加载热门选项
🔥 开始加载热门选项
🌐 发送热门选项请求: {url: "/system/questionnaireCommonOptions/popular", ...}
📡 热门选项响应: {success: true, result: [...]}
✅ 热门选项映射: [{value: "高血压", label: "高血压", ...}]

🎯 完整版搜索处理: 高
🔍 完整版搜索: 高
🌐 发送搜索请求: {url: "/system/questionnaireCommonOptions/search", ...}
📡 搜索响应: {success: true, result: [...]}
✅ API搜索结果映射: [{value: "高血压", ...}]

✅ 完整版选择: 高血压 {value: "高血压", icdCode: "I10", ...}
📊 开始记录使用频次: {fieldName: "disease", optionValue: "hypertension", ...}
📡 记录使用频次响应: {success: true, message: "记录成功"}
✅ 使用频次记录成功
```

## 🛠️ 故障排除

### 1. 下拉选项不显示
- 检查Console是否有组件挂载日志
- 检查Network是否有API请求
- 检查API响应数据格式

### 2. 搜索无反应
- 检查是否有搜索处理日志
- 检查防抖是否正常工作
- 检查API接口是否正常

### 3. 新增功能不工作
- 检查是否有"输入新的疾病名称"日志
- 检查添加新选项的API请求
- 检查后端接口和数据库

### 4. ICD10选择不工作
- 检查j-async-search-select组件
- 检查ICD字典配置
- 检查选择处理逻辑

## 📈 性能优化

### 1. 前端优化
- ✅ 防抖搜索（300ms）
- ✅ 选项数量限制
- ✅ 本地数据降级
- ✅ 组件懒加载

### 2. 后端优化
- 🔄 数据库索引优化
- 🔄 Redis缓存热门选项
- 🔄 分页查询支持
- 🔄 SQL查询优化

### 3. 用户体验
- ✅ 加载状态显示
- ✅ 错误降级处理
- ✅ 友好的时间显示
- ✅ 丰富的视觉反馈

## 🎯 测试清单

### 基础功能测试
- [ ] 组件正常挂载和初始化
- [ ] 热门选项正常加载和显示
- [ ] 搜索功能正常工作
- [ ] 选择功能正常工作

### 高级功能测试
- [ ] 新疾病自动添加
- [ ] 使用频次正确记录
- [ ] ICD10选择正常工作
- [ ] 时间显示正确

### 异常情况测试
- [ ] API失败时降级处理
- [ ] 网络异常时的表现
- [ ] 空数据时的处理
- [ ] 重复数据的处理

现在您可以使用这个完整版的疾病自动完成组件了！它包含了所有的高级功能，提供了最佳的用户体验。
