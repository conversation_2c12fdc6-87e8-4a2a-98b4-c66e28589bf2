package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.entity.QuestionnaireCommonOptions;
import org.jeecg.modules.system.mapper.QuestionnaireCommonOptionsMapper;
import org.jeecg.modules.system.service.IQuestionnaireCommonOptionsService;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.jeecg.common.util.oConvertUtils;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;

/**
 * @Description: 问卷常用选项字典
 * @Author: jeecg-boot
 * @Date: 2024-12-18
 * @Version: V1.0
 */
@Service
public class QuestionnaireCommonOptionsServiceImpl extends ServiceImpl<QuestionnaireCommonOptionsMapper, QuestionnaireCommonOptions> implements IQuestionnaireCommonOptionsService {

    @Autowired
    private QuestionnaireCommonOptionsMapper questionnaireCommonOptionsMapper;

    @Override
    public List<QuestionnaireCommonOptions> searchOptions(String fieldName, String keyword, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (oConvertUtils.isEmpty(keyword)) {
            return getPopularOptions(fieldName, limit);
        }
        return questionnaireCommonOptionsMapper.searchByFieldAndKeyword(fieldName, keyword, limit);
    }

    @Override
    public List<QuestionnaireCommonOptions> getPopularOptions(String fieldName, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return questionnaireCommonOptionsMapper.getPopularOptions(fieldName, limit);
    }

    @Override
    public void recordOptionUsage(String fieldName, String optionValue, String optionText) {
        // 先查找是否已存在该选项（按选项文本查找，因为这是用户看到的）
        QuestionnaireCommonOptions existingOption = questionnaireCommonOptionsMapper.findByFieldAndText(fieldName, optionText);
        
        if (existingOption != null) {
            // 如果存在，增加使用频次
            questionnaireCommonOptionsMapper.incrementUseCount(existingOption.getId());
            System.out.println("更新现有选项使用频次: " + optionText + ", ID: " + existingOption.getId());
        } else {
            // 如果不存在，尝试根据选项值查找
            existingOption = questionnaireCommonOptionsMapper.findByFieldAndValue(fieldName, optionValue);
            if (existingOption != null) {
                questionnaireCommonOptionsMapper.incrementUseCount(existingOption.getId());
                System.out.println("根据选项值更新使用频次: " + optionValue + ", ID: " + existingOption.getId());
            } else {
                // 如果都不存在，自动添加新选项
                System.out.println("选项不存在，自动添加新选项: " + optionText);
                addNewOption(fieldName, optionValue, optionText, "疾病", null);
            }
        }
    }

    @Override
    public void addNewOption(String fieldName, String optionValue, String optionText, String category, String icdCode) {
        // 再次检查是否已存在，避免并发添加重复数据
        QuestionnaireCommonOptions existingOption = questionnaireCommonOptionsMapper.findByFieldAndText(fieldName, optionText);
        if (existingOption != null) {
            System.out.println("选项已存在，直接更新使用频次: " + optionText);
            questionnaireCommonOptionsMapper.incrementUseCount(existingOption.getId());
            return;
        }

        QuestionnaireCommonOptions newOption = new QuestionnaireCommonOptions();
        newOption.setFieldName(fieldName);
        newOption.setOptionValue(oConvertUtils.isEmpty(optionValue) ? optionText.toLowerCase().replace("\\s+", "_") : optionValue);
        newOption.setOptionText(optionText);
        newOption.setUseCount(1);
        newOption.setLastUsedTime(new Date());
        newOption.setCategory(oConvertUtils.isEmpty(category) ? "疾病" : category);
        newOption.setIcdCode(icdCode);
        newOption.setPinyin(generatePinyin(optionText));
        newOption.setCreateTime(new Date());
        // 如果表中有del_flag字段则设置，否则忽略
        try {
            newOption.setDelFlag(0);
        } catch (Exception e) {
            // 忽略del_flag字段不存在的错误
        }
        
        boolean saveResult = this.save(newOption);
        System.out.println("新增选项结果: " + saveResult + ", 选项: " + optionText + ", ID: " + newOption.getId());
    }

    @Override
    public String generatePinyin(String text) {
        if (oConvertUtils.isEmpty(text)) {
            return "";
        }
        
        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
            format.setVCharType(HanyuPinyinVCharType.WITH_V);
            
            StringBuilder pinyin = new StringBuilder();
            for (char c : text.toCharArray()) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        pinyin.append(pinyinArray[0]);
                    }
                } else {
                    pinyin.append(c);
                }
            }
            return pinyin.toString();
        } catch (Exception e) {
            System.out.println("拼音生成失败: " + e.getMessage());
            return text.toLowerCase().replaceAll("[^a-zA-Z0-9]", "");
        }
    }

    /**
     * 智能添加选项：如果选项已存在则更新使用频次，否则新增
     */
    public void smartAddOrUpdateOption(String fieldName, String optionValue, String optionText, String category, String icdCode) {
        QuestionnaireCommonOptions existingOption = questionnaireCommonOptionsMapper.findByFieldAndText(fieldName, optionText);
        
        if (existingOption != null) {
            // 存在则更新使用频次
            questionnaireCommonOptionsMapper.incrementUseCount(existingOption.getId());
            System.out.println("智能更新: 选项已存在，更新使用频次: " + optionText);
        } else {
            // 不存在则新增
            addNewOption(fieldName, optionValue, optionText, category, icdCode);
            System.out.println("智能新增: 添加新选项: " + optionText);
        }
    }
}
