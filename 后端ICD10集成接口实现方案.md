# 后端ICD10集成接口实现方案

## 🎯 接口概述

需要实现两个新的后端接口，用于同时搜索字典表和ICD10数据表：
- `/system/questionnaireCommonOptions/searchWithIcd` - 搜索接口
- `/system/questionnaireCommonOptions/popularWithIcd` - 热门选项接口

## 📋 数据库表结构

### 1. 问卷通用选项表 (questionnaire_common_options)
```sql
CREATE TABLE questionnaire_common_options (
    id VARCHAR(32) PRIMARY KEY,
    field_name VARCHAR(50) NOT NULL COMMENT '字段名称',
    option_value VARCHAR(100) NOT NULL COMMENT '选项值',
    option_text VARCHAR(200) NOT NULL COMMENT '选项显示文本',
    use_count INT DEFAULT 0 COMMENT '使用频次',
    last_used_time DATETIME COMMENT '最后使用时间',
    category VARCHAR(50) COMMENT '分类',
    icd_code VARCHAR(20) COMMENT 'ICD编码',
    pinyin VARCHAR(100) COMMENT '拼音助记码',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_field_name (field_name),
    INDEX idx_option_text (option_text),
    INDEX idx_use_count (use_count DESC),
    INDEX idx_pinyin (pinyin)
);
```

### 2. ICD10疾病编码表 (icd10_disease)
```sql
CREATE TABLE icd10_disease (
    id VARCHAR(32) PRIMARY KEY,
    code VARCHAR(20) NOT NULL COMMENT 'ICD10编码',
    name VARCHAR(200) NOT NULL COMMENT '疾病名称',
    name_en VARCHAR(200) COMMENT '英文名称',
    category VARCHAR(100) COMMENT '分类',
    parent_code VARCHAR(20) COMMENT '父级编码',
    level INT DEFAULT 1 COMMENT '层级',
    is_leaf TINYINT DEFAULT 1 COMMENT '是否叶子节点',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_code (code),
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_parent_code (parent_code)
);
```

## 🔧 Controller实现

### QuestionnaireCommonOptionsController.java
```java
package org.jeecg.modules.system.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.system.service.IQuestionnaireCommonOptionsService;
import org.jeecg.modules.system.service.IIcd10DiseaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "问卷通用选项管理")
@RestController
@RequestMapping("/system/questionnaireCommonOptions")
@Slf4j
public class QuestionnaireCommonOptionsController {

    @Autowired
    private IQuestionnaireCommonOptionsService questionnaireCommonOptionsService;
    
    @Autowired
    private IIcd10DiseaseService icd10DiseaseService;

    /**
     * 搜索选项（包含ICD10数据）
     */
    @ApiOperation(value = "搜索选项（包含ICD10数据）", notes = "搜索选项（包含ICD10数据）")
    @GetMapping(value = "/searchWithIcd")
    public Result<Map<String, Object>> searchWithIcd(
            @RequestParam String fieldName,
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            log.info("搜索选项（含ICD10）: fieldName={}, keyword={}, limit={}", fieldName, keyword, limit);
            
            Map<String, Object> result = new HashMap<>();
            
            // 搜索字典数据
            List<QuestionnaireCommonOptions> dictData = questionnaireCommonOptionsService.searchOptions(fieldName, keyword, limit);
            result.put("dictData", dictData);
            
            // 如果是疾病字段，同时搜索ICD10数据
            if ("disease".equals(fieldName)) {
                List<Icd10Disease> icdData = icd10DiseaseService.searchByKeyword(keyword, limit);
                result.put("icdData", icdData);
            } else {
                result.put("icdData", List.of());
            }
            
            log.info("搜索结果: 字典数据{}条, ICD10数据{}条", 
                    ((List<?>) result.get("dictData")).size(),
                    ((List<?>) result.get("icdData")).size());
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("搜索选项失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门选项（包含ICD10数据）
     */
    @ApiOperation(value = "获取热门选项（包含ICD10数据）", notes = "获取热门选项（包含ICD10数据）")
    @GetMapping(value = "/popularWithIcd")
    public Result<Map<String, Object>> popularWithIcd(
            @RequestParam String fieldName,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            log.info("获取热门选项（含ICD10）: fieldName={}, limit={}", fieldName, limit);
            
            Map<String, Object> result = new HashMap<>();
            
            // 获取热门字典数据
            List<QuestionnaireCommonOptions> dictData = questionnaireCommonOptionsService.getPopularOptions(fieldName, limit);
            result.put("dictData", dictData);
            
            // 如果是疾病字段，同时获取热门ICD10数据
            if ("disease".equals(fieldName)) {
                List<Icd10Disease> icdData = icd10DiseaseService.getPopularDiseases(limit);
                result.put("icdData", icdData);
            } else {
                result.put("icdData", List.of());
            }
            
            log.info("热门选项结果: 字典数据{}条, ICD10数据{}条", 
                    ((List<?>) result.get("dictData")).size(),
                    ((List<?>) result.get("icdData")).size());
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("获取热门选项失败", e);
            return Result.error("获取热门选项失败: " + e.getMessage());
        }
    }
}
```

## 🔧 Service实现

### IQuestionnaireCommonOptionsService.java
```java
package org.jeecg.modules.system.service;

import org.jeecg.modules.system.entity.QuestionnaireCommonOptions;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

public interface IQuestionnaireCommonOptionsService extends IService<QuestionnaireCommonOptions> {
    
    /**
     * 搜索选项
     */
    List<QuestionnaireCommonOptions> searchOptions(String fieldName, String keyword, Integer limit);
    
    /**
     * 获取热门选项
     */
    List<QuestionnaireCommonOptions> getPopularOptions(String fieldName, Integer limit);
}
```

### QuestionnaireCommonOptionsServiceImpl.java
```java
package org.jeecg.modules.system.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.system.entity.QuestionnaireCommonOptions;
import org.jeecg.modules.system.mapper.QuestionnaireCommonOptionsMapper;
import org.jeecg.modules.system.service.IQuestionnaireCommonOptionsService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Service
@Slf4j
public class QuestionnaireCommonOptionsServiceImpl 
    extends ServiceImpl<QuestionnaireCommonOptionsMapper, QuestionnaireCommonOptions> 
    implements IQuestionnaireCommonOptionsService {

    @Override
    public List<QuestionnaireCommonOptions> searchOptions(String fieldName, String keyword, Integer limit) {
        QueryWrapper<QuestionnaireCommonOptions> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("field_name", fieldName);
        
        // 支持多种搜索方式
        queryWrapper.and(wrapper -> wrapper
            .like("option_text", keyword)
            .or().like("pinyin", keyword)
            .or().like("icd_code", keyword)
        );
        
        // 按使用频次降序排列
        queryWrapper.orderByDesc("use_count");
        queryWrapper.orderByDesc("last_used_time");
        
        // 限制结果数量
        queryWrapper.last("LIMIT " + limit);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<QuestionnaireCommonOptions> getPopularOptions(String fieldName, Integer limit) {
        QueryWrapper<QuestionnaireCommonOptions> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("field_name", fieldName);
        
        // 按使用频次降序排列
        queryWrapper.orderByDesc("use_count");
        queryWrapper.orderByDesc("last_used_time");
        
        // 限制结果数量
        queryWrapper.last("LIMIT " + limit);
        
        return this.list(queryWrapper);
    }
}
```

## 🔧 ICD10 Service实现

### IIcd10DiseaseService.java
```java
package org.jeecg.modules.system.service;

import org.jeecg.modules.system.entity.Icd10Disease;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

public interface IIcd10DiseaseService extends IService<Icd10Disease> {
    
    /**
     * 根据关键词搜索疾病
     */
    List<Icd10Disease> searchByKeyword(String keyword, Integer limit);
    
    /**
     * 获取热门疾病
     */
    List<Icd10Disease> getPopularDiseases(Integer limit);
}
```

### Icd10DiseaseServiceImpl.java
```java
package org.jeecg.modules.system.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.system.entity.Icd10Disease;
import org.jeecg.modules.system.mapper.Icd10DiseaseMapper;
import org.jeecg.modules.system.service.IIcd10DiseaseService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Service
@Slf4j
public class Icd10DiseaseServiceImpl 
    extends ServiceImpl<Icd10DiseaseMapper, Icd10Disease> 
    implements IIcd10DiseaseService {

    @Override
    public List<Icd10Disease> searchByKeyword(String keyword, Integer limit) {
        QueryWrapper<Icd10Disease> queryWrapper = new QueryWrapper<>();
        
        // 支持疾病名称和编码搜索
        queryWrapper.and(wrapper -> wrapper
            .like("name", keyword)
            .or().like("code", keyword)
            .or().like("name_en", keyword)
        );
        
        // 优先显示叶子节点（具体疾病）
        queryWrapper.orderByDesc("is_leaf");
        queryWrapper.orderBy(true, true, "code");
        
        // 限制结果数量
        queryWrapper.last("LIMIT " + limit);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<Icd10Disease> getPopularDiseases(Integer limit) {
        QueryWrapper<Icd10Disease> queryWrapper = new QueryWrapper<>();
        
        // 获取常见疾病（可以根据实际需求调整）
        queryWrapper.in("code", 
            "I10", "E11", "I25", "J44", "N18", 
            "K29", "J18", "M79", "R50", "R06"
        );
        
        queryWrapper.orderBy(true, true, "code");
        queryWrapper.last("LIMIT " + limit);
        
        return this.list(queryWrapper);
    }
}
```

## 📋 实体类

### QuestionnaireCommonOptions.java
```java
package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("questionnaire_common_options")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class QuestionnaireCommonOptions implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    private String fieldName;
    private String optionValue;
    private String optionText;
    private Integer useCount;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsedTime;
    
    private String category;
    private String icdCode;
    private String pinyin;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
```

### Icd10Disease.java
```java
package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("icd10_disease")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class Icd10Disease implements Serializable {
    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    
    private String code;
    private String name;
    private String nameEn;
    private String category;
    private String parentCode;
    private Integer level;
    private Integer isLeaf;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
```

## 🎯 响应格式

### 搜索接口响应
```json
{
  "success": true,
  "result": {
    "dictData": [
      {
        "id": "1",
        "fieldName": "disease",
        "optionValue": "gxy",
        "optionText": "高血压",
        "useCount": 50,
        "icdCode": "I10",
        "pinyin": "gxy"
      }
    ],
    "icdData": [
      {
        "id": "1",
        "code": "I10",
        "name": "原发性高血压",
        "nameEn": "Essential hypertension",
        "category": "循环系统疾病",
        "isLeaf": 1
      }
    ]
  }
}
```

## 🔧 Mapper接口

### QuestionnaireCommonOptionsMapper.java
```java
package org.jeecg.modules.system.mapper;

import org.jeecg.modules.system.entity.QuestionnaireCommonOptions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface QuestionnaireCommonOptionsMapper extends BaseMapper<QuestionnaireCommonOptions> {
}
```

### Icd10DiseaseMapper.java
```java
package org.jeecg.modules.system.mapper;

import org.jeecg.modules.system.entity.Icd10Disease;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface Icd10DiseaseMapper extends BaseMapper<Icd10Disease> {
}
```

## 📊 ICD10基础数据示例

### 常见疾病数据
```sql
INSERT INTO icd10_disease (id, code, name, name_en, category, is_leaf) VALUES
('1', 'I10', '原发性高血压', 'Essential hypertension', '循环系统疾病', 1),
('2', 'E11', '2型糖尿病', 'Type 2 diabetes mellitus', '内分泌疾病', 1),
('3', 'I25', '慢性缺血性心脏病', 'Chronic ischaemic heart disease', '循环系统疾病', 1),
('4', 'J44', '慢性阻塞性肺疾病', 'Chronic obstructive pulmonary disease', '呼吸系统疾病', 1),
('5', 'N18', '慢性肾脏病', 'Chronic kidney disease', '泌尿生殖系统疾病', 1),
('6', 'K29', '胃炎和十二指肠炎', 'Gastritis and duodenitis', '消化系统疾病', 1),
('7', 'J18', '肺炎', 'Pneumonia', '呼吸系统疾病', 1),
('8', 'M79', '软组织疾患', 'Soft tissue disorders', '肌肉骨骼系统疾病', 1),
('9', 'R50', '发热', 'Fever', '症状体征', 1),
('10', 'R06', '呼吸异常', 'Abnormalities of breathing', '症状体征', 1);
```

## 🧪 接口测试

### 测试搜索接口
```bash
# 搜索高血压相关疾病
curl "http://localhost:8080/jeecgboot/system/questionnaireCommonOptions/searchWithIcd?fieldName=disease&keyword=高血压&limit=10"

# 搜索ICD编码
curl "http://localhost:8080/jeecgboot/system/questionnaireCommonOptions/searchWithIcd?fieldName=disease&keyword=I10&limit=10"
```

### 测试热门选项接口
```bash
# 获取热门疾病选项
curl "http://localhost:8080/jeecgboot/system/questionnaireCommonOptions/popularWithIcd?fieldName=disease&limit=10"
```

## 🚀 部署步骤

1. **创建数据库表** - 执行上述SQL创建表结构
2. **添加实体类** - 复制QuestionnaireCommonOptions.java和Icd10Disease.java
3. **创建Mapper接口** - 添加Mapper接口文件
4. **实现Service层** - 添加Service接口和实现类
5. **添加Controller接口** - 在Controller中添加新的接口方法
6. **导入ICD10基础数据** - 执行INSERT语句导入基础数据
7. **测试接口功能** - 使用curl或Postman测试接口
8. **前端自动启用** - 前端组件将自动检测并使用新接口

## 🎯 预期效果

实现这些接口后：
- ✅ 前端组件自动启用ICD10集成功能
- ✅ 用户可以在下拉列表中看到字典数据和ICD10数据
- ✅ 选择ICD10数据时自动添加到字典表
- ✅ 搜索功能支持疾病名称、拼音、ICD编码
- ✅ 热门选项包含常用疾病和标准ICD10疾病

前端的DiseaseAutoComplete组件将无缝切换到新的集成模式！🚀
