# 构建错误修复说明

## 🔧 问题描述

构建时出现错误：
```
Could not resolve "./components/QuestionnaireCommonOptionsModal.vue" from "src/views/occu/QuestionnaireCommonOptionsList.vue"
```

## ✅ 已修复的问题

### 1. 创建缺失的模态框组件 ✅

**文件**: `QuestionnaireCommonOptionsModal.vue`
**位置**: `src/views/occu/components/QuestionnaireCommonOptionsModal.vue`

**功能特性**:
- 基于 BasicModal 和 BasicForm 组件
- 支持新增和编辑问卷通用选项
- 自动生成拼音助记码
- 完整的表单验证
- 错误处理和用户反馈

**组件结构**:
```vue
<template>
  <BasicModal @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
```

### 2. 修复路径引用问题 ✅

**问题**: 使用了错误的路径前缀 `/src/` 而不是 `/@/`

**修复内容**:
```typescript
// 修复前
import { BasicTable, useTable, TableAction } from '/src/components/Table';
import { useModal } from '/src/components/Modal';
import { downloadByOnlineUrl } from '/src/utils/file/download';
import { useMessage } from '/src/hooks/web/useMessage';

// 修复后
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { downloadByOnlineUrl } from '/@/utils/file/download';
import { useMessage } from '/@/hooks/web/useMessage';
```

### 3. 表单配置完整性 ✅

**表单字段**:
- `fieldName`: 字段名称（疾病、症状、药物、检查项目）
- `optionValue`: 选项值（用于程序识别）
- `optionText`: 选项显示文本（用户看到的文本）
- `useCount`: 使用频次
- `category`: 分类
- `icdCode`: ICD编码
- `pinyin`: 拼音助记码（可自动生成）

**验证规则**:
- 字段名称：必填
- 选项值：必填
- 选项显示文本：必填
- 使用频次：必填，最小值0

### 4. API集成完整性 ✅

**使用的API方法**:
- `saveOrUpdateQuestionnaireOptions`: 保存或更新
- `getQuestionnaireCommonOptionsList`: 列表查询
- `deleteQuestionnaireOptions`: 单个删除
- `batchDeleteQuestionnaireOptions`: 批量删除
- `exportXls`: 导出Excel
- `importExcel`: 导入Excel

**API参数格式**:
```typescript
// 新增
{
  fieldName: 'disease',
  optionValue: 'gxy',
  optionText: '高血压',
  useCount: 0,
  category: '疾病',
  icdCode: 'I10',
  pinyin: 'gxy'
}

// 更新（包含ID）
{
  id: '123456',
  fieldName: 'disease',
  optionValue: 'gxy',
  optionText: '高血压',
  useCount: 50,
  category: '疾病',
  icdCode: 'I10',
  pinyin: 'gxy'
}
```

## 🎯 功能特性

### 自动拼音生成
```typescript
function generatePinyin(text: string): string {
  const pinyinMap: Record<string, string> = {
    '高血压': 'gxy',
    '糖尿病': 'tnb', 
    '冠心病': 'gxb',
    // ... 更多映射
  };
  
  return pinyinMap[text] || text.toLowerCase().replace(/[^a-z0-9]/g, '').substring(0, 10);
}
```

### 表单默认值
- 新增时：`useCount: 0`, `category: '疾病'`
- 编辑时：使用现有数据

### 数据验证
- 必填字段验证
- 数据类型验证
- 数值范围验证

## 🔍 使用方法

### 在列表页面中使用
```vue
<template>
  <div>
    <BasicTable @register="registerTable">
      <!-- 表格内容 -->
    </BasicTable>
    
    <!-- 模态框组件 -->
    <QuestionnaireCommonOptionsModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script setup>
import QuestionnaireCommonOptionsModal from './components/QuestionnaireCommonOptionsModal.vue';

// 新增
function handleAdd() {
  openModal(true, {
    isUpdate: false,
  });
}

// 编辑
function handleEdit(record) {
  openModal(true, {
    record,
    isUpdate: true,
  });
}
</script>
```

## 🧪 测试场景

### 测试场景1：新增选项
1. 点击"新增"按钮
2. 填写表单字段
3. 点击确定保存
4. 验证数据是否正确保存

### 测试场景2：编辑选项
1. 点击列表中的"编辑"按钮
2. 修改表单字段
3. 点击确定保存
4. 验证数据是否正确更新

### 测试场景3：自动拼音生成
1. 输入疾病名称（如"高血压"）
2. 拼音字段应自动填充为"gxy"
3. 可手动修改拼音字段

## 🚀 构建验证

修复后，构建应该能够成功完成：

```bash
# 开发环境
npm run dev

# 生产构建
npm run build
```

## 📋 文件清单

**新增文件**:
- `src/views/occu/components/QuestionnaireCommonOptionsModal.vue`

**修改文件**:
- `src/views/occu/QuestionnaireCommonOptionsList.vue`（路径修复）

**依赖文件**（已存在）:
- `src/views/occu/components/QuestionnaireCommonOptions.data.ts`
- `src/views/occu/components/questionnaireCommonOptions.api.ts`

现在构建错误应该已经解决，项目可以正常构建和运行了！
