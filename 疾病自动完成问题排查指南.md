# 疾病自动完成功能问题排查指南

## 问题现象
在输入疾病名称时，没有看到自动填充的网络请求。

## 排查步骤

### 1. 检查组件是否正确加载

打开浏览器开发者工具（F12），查看Console控制台：

1. **查看组件挂载日志**：
   - 应该看到：`🚀 DiseaseAutoCompleteTest 组件已挂载，开始加载热门选项`
   - 如果没有看到，说明组件没有正确加载

2. **查看热门选项加载日志**：
   - 应该看到：`🔥 开始加载热门选项`
   - 应该看到：`✅ 热门选项: [...]`

### 2. 检查搜索功能

在疾病名称输入框中输入文字（如"高"），查看Console：

1. **搜索触发日志**：
   - 应该看到：`🎯 触发搜索处理: 高`
   - 应该看到：`🔍 开始搜索疾病: 高`

2. **搜索结果日志**：
   - 应该看到：`✅ 搜索结果: [...]`

### 3. 检查下拉选项显示

1. **点击输入框**：应该显示热门选项下拉列表
2. **输入文字**：应该显示过滤后的选项
3. **选项格式**：每个选项应该显示疾病名称、ICD编码和使用次数

### 4. 检查网络请求（原版组件）

如果使用原版的 `DiseaseAutoComplete` 组件：

1. **打开Network面板**
2. **输入疾病名称**
3. **查看是否有以下请求**：
   - `/system/questionnaireCommonOptions/search`
   - `/system/questionnaireCommonOptions/popular`

### 5. 常见问题及解决方案

#### 问题1：组件没有挂载
**现象**：Console中没有看到组件挂载日志
**解决**：
1. 检查组件导入路径是否正确
2. 检查组件名称是否正确注册
3. 检查父组件是否正确渲染

#### 问题2：搜索不触发
**现象**：输入文字但没有搜索日志
**解决**：
1. 检查 `@search` 事件是否正确绑定
2. 检查 `handleSearch` 方法是否正确定义
3. 检查防抖函数是否正常工作

#### 问题3：网络请求失败（原版组件）
**现象**：看到网络请求但返回错误
**解决**：
1. 检查后端服务是否启动
2. 检查API接口路径是否正确
3. 检查数据库表是否存在
4. 检查跨域配置

#### 问题4：下拉选项不显示
**现象**：有搜索结果但下拉不显示
**解决**：
1. 检查 `options` 数据格式是否正确
2. 检查 `a-auto-complete` 组件配置
3. 检查CSS样式是否影响显示

## 测试步骤

### 使用测试版本组件

1. **确认使用测试组件**：
   ```vue
   import DiseaseAutoCompleteTest from './DiseaseAutoCompleteTest.vue';
   ```

2. **测试基本功能**：
   - 点击输入框，应该显示热门疾病列表
   - 输入"高"，应该显示包含"高"的疾病
   - 输入"I10"，应该显示高血压（ICD编码匹配）

3. **测试选择功能**：
   - 点击选择一个疾病
   - 查看Console是否有选择日志
   - 确认输入框值是否正确更新

### 切换到正式版本

测试版本正常后，切换到正式版本：

1. **修改导入**：
   ```vue
   import DiseaseAutoComplete from './DiseaseAutoComplete.vue';
   ```

2. **修改组件使用**：
   ```vue
   <DiseaseAutoComplete ... />
   ```

3. **检查网络请求**：
   - 确保后端API正常
   - 检查数据库表结构
   - 验证接口返回数据

## 调试技巧

### 1. 使用Console日志
所有关键步骤都有详细的Console日志，通过日志可以快速定位问题。

### 2. 使用Vue DevTools
安装Vue DevTools浏览器插件，可以查看组件状态和数据流。

### 3. 网络面板调试
在Network面板中可以查看：
- 请求URL和参数
- 响应状态和数据
- 请求耗时

### 4. 断点调试
在关键方法中设置断点：
- `handleSearch` 方法
- `debouncedSearch` 方法
- `loadPopularOptions` 方法

## 预期行为

### 正常工作流程：
1. 组件挂载 → 加载热门选项
2. 点击输入框 → 显示热门选项下拉
3. 输入文字 → 触发搜索 → 显示过滤结果
4. 选择选项 → 更新输入框值 → 记录使用频次

### 测试版本特点：
- 使用本地测试数据
- 不依赖后端API
- 有详细的Console日志
- 模拟网络延迟

### 正式版本特点：
- 调用后端API
- 实时数据更新
- 使用频次统计
- 支持拼音搜索

## 下一步操作

1. **先测试测试版本**，确保基本功能正常
2. **检查Console日志**，确认每个步骤都正常执行
3. **验证用户交互**，确保选择和输入都正常
4. **切换到正式版本**，检查网络请求
5. **排查后端问题**，确保API和数据库正常

如果测试版本都不工作，说明是前端组件问题；如果测试版本正常但正式版本不工作，说明是后端API问题。
