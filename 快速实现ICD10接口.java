/**
 * 快速实现ICD10集成接口
 * 在现有的QuestionnaireCommonOptionsController中添加以下方法
 */

/**
 * 搜索选项（包含ICD10数据）
 */
@ApiOperation(value = "搜索选项（包含ICD10数据）", notes = "搜索选项（包含ICD10数据）")
@GetMapping(value = "/searchWithIcd")
public Result<Map<String, Object>> searchWithIcd(
        @RequestParam String fieldName,
        @RequestParam String keyword,
        @RequestParam(defaultValue = "10") Integer limit) {
    
    try {
        log.info("搜索选项（含ICD10）: fieldName={}, keyword={}, limit={}", fieldName, keyword, limit);
        
        Map<String, Object> result = new HashMap<>();
        
        // 1. 搜索现有字典数据
        QueryWrapper<QuestionnaireCommonOptions> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("field_name", fieldName);
        queryWrapper.and(wrapper -> wrapper
            .like("option_text", keyword)
            .or().like("pinyin", keyword)
            .or().like("icd_code", keyword)
        );
        queryWrapper.orderByDesc("use_count");
        queryWrapper.last("LIMIT " + limit);
        
        List<QuestionnaireCommonOptions> dictData = questionnaireCommonOptionsService.list(queryWrapper);
        result.put("dictData", dictData);
        
        // 2. 如果是疾病字段，添加模拟的ICD10数据
        List<Map<String, Object>> icdData = new ArrayList<>();
        if ("disease".equals(fieldName)) {
            // 模拟ICD10数据（后续可以替换为真实的ICD10表查询）
            List<Map<String, String>> mockIcdData = Arrays.asList(
                Map.of("code", "I10", "name", "原发性高血压"),
                Map.of("code", "E11", "name", "2型糖尿病"),
                Map.of("code", "I25", "name", "慢性缺血性心脏病"),
                Map.of("code", "J44", "name", "慢性阻塞性肺疾病"),
                Map.of("code", "N18", "name", "慢性肾脏病"),
                Map.of("code", "K29", "name", "胃炎和十二指肠炎"),
                Map.of("code", "J18", "name", "肺炎"),
                Map.of("code", "M79", "name", "软组织疾患"),
                Map.of("code", "R50", "name", "发热"),
                Map.of("code", "R06", "name", "呼吸异常")
            );
            
            // 过滤匹配关键词的ICD10数据
            for (Map<String, String> icd : mockIcdData) {
                if (icd.get("name").contains(keyword) || icd.get("code").contains(keyword)) {
                    Map<String, Object> icdItem = new HashMap<>();
                    icdItem.put("code", icd.get("code"));
                    icdItem.put("name", icd.get("name"));
                    icdItem.put("icdCode", icd.get("code"));
                    icdItem.put("icdName", icd.get("name"));
                    icdData.add(icdItem);
                    
                    if (icdData.size() >= limit) break;
                }
            }
        }
        result.put("icdData", icdData);
        
        log.info("搜索结果: 字典数据{}条, ICD10数据{}条", dictData.size(), icdData.size());
        
        return Result.ok(result);
    } catch (Exception e) {
        log.error("搜索选项失败", e);
        return Result.error("搜索失败: " + e.getMessage());
    }
}

/**
 * 获取热门选项（包含ICD10数据）
 */
@ApiOperation(value = "获取热门选项（包含ICD10数据）", notes = "获取热门选项（包含ICD10数据）")
@GetMapping(value = "/popularWithIcd")
public Result<Map<String, Object>> popularWithIcd(
        @RequestParam String fieldName,
        @RequestParam(defaultValue = "10") Integer limit) {
    
    try {
        log.info("获取热门选项（含ICD10）: fieldName={}, limit={}", fieldName, limit);
        
        Map<String, Object> result = new HashMap<>();
        
        // 1. 获取热门字典数据
        QueryWrapper<QuestionnaireCommonOptions> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("field_name", fieldName);
        queryWrapper.orderByDesc("use_count");
        queryWrapper.orderByDesc("last_used_time");
        queryWrapper.last("LIMIT " + limit);
        
        List<QuestionnaireCommonOptions> dictData = questionnaireCommonOptionsService.list(queryWrapper);
        result.put("dictData", dictData);
        
        // 2. 如果是疾病字段，添加热门ICD10数据
        List<Map<String, Object>> icdData = new ArrayList<>();
        if ("disease".equals(fieldName)) {
            // 热门疾病的ICD10数据
            List<Map<String, String>> popularIcdData = Arrays.asList(
                Map.of("code", "I10", "name", "原发性高血压"),
                Map.of("code", "E11", "name", "2型糖尿病"),
                Map.of("code", "I25", "name", "慢性缺血性心脏病"),
                Map.of("code", "J44", "name", "慢性阻塞性肺疾病"),
                Map.of("code", "N18", "name", "慢性肾脏病")
            );
            
            for (Map<String, String> icd : popularIcdData) {
                Map<String, Object> icdItem = new HashMap<>();
                icdItem.put("code", icd.get("code"));
                icdItem.put("name", icd.get("name"));
                icdItem.put("icdCode", icd.get("code"));
                icdItem.put("icdName", icd.get("name"));
                icdData.add(icdItem);
                
                if (icdData.size() >= limit) break;
            }
        }
        result.put("icdData", icdData);
        
        log.info("热门选项结果: 字典数据{}条, ICD10数据{}条", dictData.size(), icdData.size());
        
        return Result.ok(result);
    } catch (Exception e) {
        log.error("获取热门选项失败", e);
        return Result.error("获取热门选项失败: " + e.getMessage());
    }
}

/**
 * 需要添加的导入语句
 */
/*
import java.util.*;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
*/

/**
 * 使用说明：
 * 
 * 1. 将上述两个方法添加到现有的QuestionnaireCommonOptionsController类中
 * 2. 确保添加必要的导入语句
 * 3. 重启后端服务
 * 4. 前端组件将自动检测并使用新接口
 * 
 * 这是一个快速实现版本，使用了模拟的ICD10数据。
 * 后续可以创建真实的ICD10数据表并替换模拟数据。
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "result": {
 *     "dictData": [...],  // 字典表数据
 *     "icdData": [...]    // ICD10数据
 *   }
 * }
 */
