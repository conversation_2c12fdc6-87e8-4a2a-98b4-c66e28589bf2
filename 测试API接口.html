<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试疾病自动完成API接口</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>疾病自动完成API接口测试</h1>
    
    <div class="test-section">
        <h3>1. 测试热门选项接口</h3>
        <button class="test-button" onclick="testPopularOptions()">测试热门选项</button>
        <div id="popular-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试搜索接口</h3>
        <input type="text" id="search-keyword" placeholder="输入搜索关键词" value="高">
        <button class="test-button" onclick="testSearch()">测试搜索</button>
        <div id="search-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试记录使用频次接口</h3>
        <input type="text" id="option-value" placeholder="选项值" value="hypertension">
        <input type="text" id="option-text" placeholder="选项文本" value="高血压">
        <button class="test-button" onclick="testRecordUsage()">测试记录使用</button>
        <div id="record-result" class="result"></div>
    </div>

    <script>
        // 获取基础URL（假设前端和后端在同一域名下）
        const baseURL = window.location.origin;
        
        async function makeRequest(url, options = {}) {
            try {
                console.log('发送请求:', url, options);
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                console.log('响应数据:', data);
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                console.error('请求失败:', error);
                return { success: false, error: error.message };
            }
        }

        async function testPopularOptions() {
            const resultDiv = document.getElementById('popular-result');
            resultDiv.textContent = '正在测试...';
            
            const url = `${baseURL}/system/questionnaireCommonOptions/popular?fieldName=disease&limit=10`;
            const result = await makeRequest(url);
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 成功!\n状态码: ${result.status}\n响应数据: ${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败!\n错误: ${result.error || '请求失败'}\n状态码: ${result.status || 'N/A'}`;
            }
        }

        async function testSearch() {
            const keyword = document.getElementById('search-keyword').value;
            const resultDiv = document.getElementById('search-result');
            resultDiv.textContent = '正在测试...';
            
            const url = `${baseURL}/system/questionnaireCommonOptions/search?fieldName=disease&keyword=${encodeURIComponent(keyword)}&limit=10`;
            const result = await makeRequest(url);
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 成功!\n状态码: ${result.status}\n响应数据: ${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败!\n错误: ${result.error || '请求失败'}\n状态码: ${result.status || 'N/A'}`;
            }
        }

        async function testRecordUsage() {
            const optionValue = document.getElementById('option-value').value;
            const optionText = document.getElementById('option-text').value;
            const resultDiv = document.getElementById('record-result');
            resultDiv.textContent = '正在测试...';
            
            const url = `${baseURL}/system/questionnaireCommonOptions/recordUsage`;
            const params = new URLSearchParams({
                fieldName: 'disease',
                optionValue: optionValue,
                optionText: optionText
            });
            
            const result = await makeRequest(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            });
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 成功!\n状态码: ${result.status}\n响应数据: ${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 失败!\n错误: ${result.error || '请求失败'}\n状态码: ${result.status || 'N/A'}`;
            }
        }

        // 页面加载时自动测试热门选项
        window.onload = function() {
            console.log('页面加载完成，开始自动测试热门选项接口');
            testPopularOptions();
        };
    </script>
</body>
</html>
