# 疾病自动完成新增功能说明

## 功能概述

现在疾病自动完成组件支持自动新增功能：
- 当用户输入的疾病名称在数据库中不存在时，会自动添加到问卷常用选项字典中
- 自动记录使用频次，新增的选项初始使用频次为1
- 支持拼音助记码自动生成

## 触发条件

### 1. 用户直接输入新疾病名称
- 用户在输入框中输入疾病名称
- 按回车键或输入框失去焦点
- 系统检查该疾病名称是否存在于数据库中
- 如果不存在，自动添加

### 2. 用户从下拉选项中选择
- 如果选择的是现有选项，只更新使用频次
- 如果选择的是新选项，先添加再更新使用频次

## 工作流程

### 场景1：输入新疾病名称
```
用户输入: "新型冠状病毒肺炎"
↓
系统检查: 数据库中不存在
↓
自动添加: 
- fieldName: "disease"
- optionValue: "xinxingguanzhuangbingdufeiyan" (自动生成)
- optionText: "新型冠状病毒肺炎"
- useCount: 1
- category: "疾病"
- pinyin: "xinxingguanzhuangbingdufeiyan" (自动生成)
↓
记录使用: 使用频次 +1 (变为1)
```

### 场景2：输入已存在的疾病名称
```
用户输入: "高血压"
↓
系统检查: 数据库中已存在
↓
直接记录使用: 使用频次 +1
```

## 前端实现

### 关键事件处理
```vue
<a-auto-complete
  @blur="handleInputConfirm(inputValue)"
  @pressEnter="handleInputConfirm(inputValue)"
  @select="handleSelect"
/>
```

### 核心方法
1. **handleInputConfirm**: 处理用户直接输入确认
2. **addNewOption**: 添加新选项到数据库
3. **handleSelect**: 处理选项选择和使用频次记录

## 后端实现

### API接口

#### 1. 添加新选项
```
POST /system/questionnaireCommonOptions/add
Content-Type: application/json

{
  "fieldName": "disease",
  "optionValue": "xinxingguanzhuangbingdufeiyan",
  "optionText": "新型冠状病毒肺炎",
  "useCount": 1,
  "category": "疾病",
  "pinyin": "xinxingguanzhuangbingdufeiyan"
}
```

#### 2. 记录使用频次
```
POST /system/questionnaireCommonOptions/recordUsage
Content-Type: application/x-www-form-urlencoded

fieldName=disease&optionValue=xinxingguanzhuangbingdufeiyan&optionText=新型冠状病毒肺炎
```

### 智能处理逻辑
```java
public void recordOptionUsage(String fieldName, String optionValue, String optionText) {
    // 1. 先按选项文本查找
    QuestionnaireCommonOptions existingOption = findByFieldAndText(fieldName, optionText);
    
    if (existingOption != null) {
        // 2. 存在则更新使用频次
        incrementUseCount(existingOption.getId());
    } else {
        // 3. 不存在则自动添加新选项
        addNewOption(fieldName, optionValue, optionText, "疾病", null);
    }
}
```

## 数据结构

### 新增选项的默认值
```sql
INSERT INTO questionnaire_common_options (
    field_name,           -- "disease"
    option_value,         -- 自动生成的英文值
    option_text,          -- 用户输入的中文名称
    use_count,            -- 1 (初始使用频次)
    last_used_time,       -- 当前时间
    category,             -- "疾病"
    icd_code,             -- NULL (可后续补充)
    pinyin,               -- 自动生成的拼音
    create_time,          -- 当前时间
    del_flag              -- 0 (正常状态)
) VALUES (
    'disease',
    'xinxingguanzhuangbingdufeiyan',
    '新型冠状病毒肺炎',
    1,
    NOW(),
    '疾病',
    NULL,
    'xinxingguanzhuangbingdufeiyan',
    NOW(),
    0
);
```

## 用户体验

### 1. 无感知添加
- 用户正常输入疾病名称
- 系统自动判断是否需要新增
- 无需额外操作，体验流畅

### 2. 智能学习
- 新输入的疾病会自动加入常用选项
- 下次输入时会出现在自动完成列表中
- 使用频次越高，排序越靠前

### 3. 数据质量
- 自动生成拼音助记码，支持拼音搜索
- 统一的数据格式和分类
- 避免重复数据

## 测试场景

### 1. 基本新增测试
1. 输入一个全新的疾病名称（如"测试疾病A"）
2. 按回车或点击其他地方
3. 检查Console日志，应该看到添加新选项的日志
4. 检查Network面板，应该有添加和记录使用的API请求

### 2. 重复输入测试
1. 再次输入相同的疾病名称
2. 系统应该识别为已存在，只更新使用频次
3. 不应该重复添加

### 3. 下拉选择测试
1. 输入部分疾病名称，从下拉中选择
2. 如果是新选项，应该先添加再记录使用
3. 如果是现有选项，只记录使用

## 注意事项

### 1. 并发处理
- 后端有重复检查机制，避免并发添加重复数据
- 如果检测到已存在，会转为更新使用频次

### 2. 数据验证
- 输入的疾病名称不能为空
- 自动去除首尾空格
- 生成的optionValue会转为小写并替换空格

### 3. 错误处理
- API调用失败时有降级处理
- 不会影响用户的正常输入体验
- 有详细的Console日志用于调试

## 监控和维护

### 1. 数据监控
```sql
-- 查看新增的选项
SELECT * FROM questionnaire_common_options 
WHERE field_name = 'disease' 
ORDER BY create_time DESC 
LIMIT 20;

-- 查看使用频次统计
SELECT option_text, use_count, last_used_time 
FROM questionnaire_common_options 
WHERE field_name = 'disease' 
ORDER BY use_count DESC;
```

### 2. 数据清理
```sql
-- 清理低频使用的选项（可选）
DELETE FROM questionnaire_common_options 
WHERE field_name = 'disease' 
AND use_count = 1 
AND create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

这个功能让系统具备了自学习能力，用户使用越多，系统的疾病词库就越丰富，自动完成的效果也会越来越好。
