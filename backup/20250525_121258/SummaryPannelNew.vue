<template>
  <div style="padding: 5px">
    <customer-reg-list4-summary ref="customerRegList" @row-click="handleRegTableRowClick" v-show="showRegList" />

    <div v-show="!showRegList">
      <a-alert message="请在人员列表中选择人员！" type="warning" v-if="!currentReg?.id" />
      <template v-else>
        <div class="exam-info-single-row" v-if="currentReg?.id">
          <span class="info-value strong" style="font-size: 14px"
            >{{ currentReg.name || '' }} {{ currentReg.gender || '' }} {{ currentReg.age ? currentReg.age + currentReg.ageUnit : '' }}</span
          >
          <a-tag class="exam-no">{{ currentReg.examNo ? currentReg.examNo : '' }}</a-tag>

          <span class="info-divider">|</span>

          <span class="info-label">状态:</span>
          <span class="info-value strong">{{ customerSummary.status || '未总检' }}</span>
          <a-tag v-if="customerSummary.initailDoctor" color="cyan" size="small"> 初:{{ customerSummary.initailDoctor }} </a-tag>
          <a-tag v-if="customerSummary.chiefDoctor" color="blue" size="small">主:{{ customerSummary.chiefDoctor }} </a-tag>
          <a-tag v-if="customerSummary.auditor" color="green" size="small">审:{{ customerSummary.auditor }}</a-tag>

          <span class="info-divider">|</span>

          <span class="info-label">类别:</span>
          <span class="info-value">{{ currentReg.examCategory || '' }}</span>

          <span class="info-divider">|</span>

          <span class="info-label">危害因素:</span>
          <span class="info-value">{{ currentReg.riskFactor ? currentReg.riskFactor_dictText : '' }}</span>

          <span class="info-divider">|</span>

          <span class="info-label">单位:</span>
          <span class="info-value">{{ currentReg.companyName ? currentReg.companyName : '' }}</span>

          <a-button type="link" class="return-list-icon" @click="returnToList" title="列表">
            <ArrowLeftOutlined />
            列表
          </a-button>
        </div>
      </template>
      <splitpanes class="default-theme">
        <pane size="25" style="padding: 2px">
          <a-card size="small">
            <template #extra>
              <a-space>
                <a-button @click="previewAllPic">全部图片</a-button>
                <a-button @click="openDataModal">历史数据</a-button>
                <a-spin :spinning="btnLoading">
                  <a-dropdown>
                    <template #overlay>
                      <a-menu @click="handleMenuClick">
                        <a-menu-item key="fixLisData"> 更新检验数据</a-menu-item>
                        <a-menu-item key="fixCheckData"> 更新检查数据</a-menu-item>
                        <a-menu-item key="navLis"> 检验系统</a-menu-item>
                        <a-menu-item key="navCheck"> 检查系统</a-menu-item>
                        <a-menu-item key="occuHistory"> 职业史</a-menu-item>
                        <a-menu-item key="printApply"> 打印导引单</a-menu-item>
                        <a-menu-item key="printerSetup"> 打印机设置</a-menu-item>
                        <a-menu-item key="healthQuest"> 健康问卷</a-menu-item>
                      </a-menu>
                    </template>

                    <a-button size="small">
                      更多
                      <DownOutlined />
                    </a-button>
                  </a-dropdown>
                </a-spin>
              </a-space>
            </template>
            <div style="padding: 0; height: 76vh; overflow-y: auto">
              <customer-reg-item-group-status ref="itemGroupStatus" @load-data="handleItemGroupStatus" />
            </div>
          </a-card>
        </pane>
        <pane size="75">
          <a-alert v-if="criticalItems.length > 0" :message="criticalTip" type="error" show-icon style="margin-bottom: 6px" />
          <a-alert
            v-if="showAuditTip"
            :message="'总检驳回：' + auditRecord.rejectReason"
            type="error"
            show-icon
            style="margin-bottom: 6px"
            :closable="true"
          />
          <a-tabs
            tabPosition="right"
            class="vertical-tab-text"
            :tabBarStyle="{ width: '14px' }"
            v-model:activeKey="summaryPannelKey"
            @change="handleSummaryPannelTab"
          >
            <a-tab-pane key="health">
              <template #tab>
                <div class="vertical-text">汇总和建议</div>
              </template>

              <a-card size="small" :bordered="false">
                <template #extra></template>
                <a-row :gutter="4">
                  <a-col :span="12">
                    <AbnormalSummaryEditor3
                      ref="abnormalSummaryEditorRef"
                      @add-advice="handleAddAdvice"
                      :readOnly="
                        customerSummary.status == '审核通过' || (currentReg.reportEditLockFlag == '1' && user.username != currentReg.reportEditLockBy)
                      "
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-card
                      size="small"
                      style="width: 100%"
                      :bordered="false"
                      :tab-list="[
                        {
                          key: 'diagnosis',
                          tab: '诊断',
                        },
                        {
                          key: 'advice',
                          tab: '建议',
                        },
                      ]"
                      :active-tab-key="summaryTabKey"
                      @tab-change="(key) => handleSummaryTabChange(key)"
                    >
                      <div style="height: 70vh; overflow-y: auto" v-show="summaryTabKey === 'diagnosis'">
                        <DiagnosisEditor v-model="diagnosisList" @change="handleDiagnosChange" />
                      </div>

                      <AdviceEditor3
                        v-show="summaryTabKey === 'advice'"
                        ref="adviceEditorRef"
                        :currentReg="currentReg"
                        :readOnly="
                          customerSummary.status != '审核通过' &&
                          (currentReg.reportEditLockFlag != '1' || user.username == currentReg.reportEditLockBy)
                        "
                        @get-ai-summary="handleGetAiSummary"
                      />
                    </a-card>
                  </a-col>
                </a-row>
              </a-card>
            </a-tab-pane>
            <a-tab-pane key="occu">
              <template #tab>
                <div class="vertical-text">职业检</div>
              </template>
              <a-card size="small">
                <ZyConclusionDetailList ref="zyConclusionDetailList" />
              </a-card>
            </a-tab-pane>
            <a-tab-pane key="healthCard">
              <template #tab>
                <div class="vertical-text">健康证</div>
              </template>
              <a-card size="small">
                <template #extra>
                  <a-space>
                    <a-button type="primary" @click="saveHealthCardSummary">保存</a-button>
                  </a-space>
                </template>
                <div style="height: 70vh; overflow-y: auto">
                  <a-form
                    ref="formRef"
                    class="antd-modal-form"
                    :labelCol="{ xs: { span: 24 }, sm: { span: 5 } }"
                    :wrapperCol="{ xs: { span: 24 }, sm: { span: 19 } }"
                  >
                    <a-row>
                      <a-col :span="24">
                        <a-form-item label="结果">
                          <a-radio-group v-model:value="customerSummary.healthCardResult">
                            <a-radio-button value="合格">合格</a-radio-button>
                            <a-radio-button value="不合格">不合格</a-radio-button>
                          </a-radio-group>
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </a-form>
                </div>
              </a-card>
            </a-tab-pane>
          </a-tabs>

          <div class="bottom-action-bar" v-if="summaryPannelKey === 'health' || summaryPannelKey === 'occu'">
            <div class="action-bar-content">
              <div class="left-actions"></div>

              <div class="right-actions">
                <a-button
                  type="primary"
                  @click="saveOrUpdateSummary('pre')"
                  :disabled="customerSummary.status == '审核通过'"
                  v-if="hasPermission('summary:presave')"
                >
                  <SaveOutlined />
                  保存初检
                </a-button>
                <a-button
                  type="primary"
                  :disabled="customerSummary.status == '审核通过'"
                  @click="saveOrUpdateSummary('normal')"
                  v-if="hasPermission('summary:customer_reg_summary:add')"
                  style="margin-left: 8px"
                >
                  <SaveOutlined />
                  保存主检
                </a-button>
                <a-button type="primary" @click="openAuditModal" v-if="hasPermission('summary:summary_audit_record:add')" style="margin-left: 8px">
                  <AuditOutlined />
                  审核
                </a-button>
                <a-button
                  type="primary"
                  @click="revokeAudit"
                  v-if="
                    (currentReg.summaryStatus == '审核通过' || currentReg.summaryStatus == '驳回') &&
                    hasPermission('summary:summary_audit_record:add')
                  "
                  style="margin-left: 8px"
                >
                  <ClearOutlined />
                  撤销
                </a-button>
                <a-button type="dashed" @click="openAuditRecordModal" style="margin-left: 8px">
                  <HistoryOutlined />
                </a-button>
                <a-divider type="vertical" />
                <a-spin :spinning="reportDataLoading">
                  <a-radio-group>
                    <a-radio-button value="large" @click="previewReport">预览</a-radio-button>
                    <a-radio-button value="default" @click="printReport">
                      <PrinterOutlined />
                      打印
                    </a-radio-button>
                  </a-radio-group>
                  <a-select
                    placeholder="请选择报告模版"
                    v-model:value="currentReportTemplateId"
                    style="width: 120px; margin-left: 8px"
                    :options="reportTemplateList"
                  />
                </a-spin>
              </div>
            </div>
          </div>

          <a-float-button
            shape="square"
            description="返回列表"
            type="default"
            :style="{
              right: '4px',
              bottom: '165px',
            }"
            @click="returnToList"
          >
            <template #icon>
              <ArrowLeftOutlined />
            </template>
          </a-float-button>

          <a-float-button
            shape="square"
            description="科室提醒"
            type="primary"
            :style="{
              right: '4px',
              bottom: '95px',
            }"
            :badge="{ count: departmentTipCount, showZero: false }"
            @click="openDepartTipDrawer"
          >
            <template #icon>
              <AlertOutlined />
            </template>
          </a-float-button>
        </pane>
      </splitpanes>
    </div>

    <!--    <Report ref="reportRef" />-->
    <upload-manage-modal :preview-file-list="fileList" title="图片管理" ref="registerUploadModal" @ok="handlePicChange" />
    <customer-reg-depart-tip-drawer
      ref="departTipRef"
      :regId="currentReg?.id"
      :departmentId="currentDepartments"
      @loaded="handleDepartTipDrawerLoaded"
    />
    <zy-inquiry-modal ref="inquiryModal" />
    <jimu-report-modal ref="jimuReportModal" title="历史结果" width="80%" :report-id="historyResultReportId" />
    <summary-audit-record-modal ref="summaryAuditRecordModal" @success="handleAuditOk" />
    <summary-audit-record-list-modal ref="summaryAuditRecordListModal" />
    <recheck-notify-pannel-modal ref="recheckNotifyPannelModal" @cancel="countRecheck" />
    <editor-modal ref="editorModal" @ok="setDepartSummaryText" />
    <PrinterSetupModal ref="printerSetupModalRef" />
    <report-vue ref="reportRef" />
    <HealthQuestAddModal ref="healthQuestAddModal" @ok="handleQuestOk" />
  </div>
</template>
<script lang="ts" setup name="SummaryPannel">
  import { computed, nextTick, onBeforeUnmount, onMounted, provide, reactive, ref, unref } from 'vue';
  import { AdviceBean, CustomerRegCriticalItem, CustomerRegSummary, ICustomerReg, ICustomerRegItemResult } from '#/types';
  import { fetchCheckData, fetchLisData, getCriticalItem, saveItemResult } from '@/views/station/Station.api';
  import {
    listAbnormalSummaryByRegV2,
    revokeSummaryStatus,
    saveHealthCardResult,
    saveSummary,
    updateReportPrintTimes,
  } from '@/views/summary/Summary.api';
  import { list as listAditRecord } from './SummaryAuditRecord.api';
  import { Form, message, SelectProps, theme } from 'ant-design-vue';
  import UploadManageModal from '@/components/Upload/src/UploadManageModal.vue';
  import { preview } from 'vue3-image-preview';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useUserStore } from '@/store/modules/user';
  import CustomerRegDepartTipDrawer from '@/views/station/CustomerRegDepartTipDrawer.vue';
  import {
    AlertOutlined,
    ArrowLeftOutlined,
    AuditOutlined,
    ClearOutlined,
    DownOutlined,
    HistoryOutlined,
    PrinterOutlined,
    SaveOutlined,
  } from '@ant-design/icons-vue';

  import ZyInquiryModal from '@/views/occu/components/ZyInquiryModal.vue';
  import JimuReportModal from '@/components/Report/JimuReportModal.vue';
  import CustomerRegList4Summary from '@/views/summary/CustomerRegList4Summary.vue';
  import _ from 'lodash';
  import SummaryAuditRecordModal from '@/views/summary/components/SummaryAuditRecordModal.vue';
  import SummaryAuditRecordListModal from '@/views/summary/components/SummaryAuditRecordListModal.vue';
  import RecheckNotifyPannelModal from '@/views/recheck/RecheckNotifyPannelModal.vue';
  import { countByCustomerRegId } from '@/views/recheck/RecheckNotify.api';
  import ZyConclusionDetailList from '@/views/summary/ZyConclusionDetailList.vue';
  import { getReportData } from '@/views/summary/CustomerRegSummary.api';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { getDefaultIdOfType, getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { PrinterType, printReportDirect } from '@/utils/print';
  import EditorModal from '@/views/summary/EditorModal.vue';
  import PrinterSetupModal from '@/views/reg/PrinterSetupModal.vue';
  import { getGuidanceSheet, updateGuidancePrintTimes, updateHealthQuestId } from '@/views/reg/CustomerReg.api';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { buildUUID } from '@/utils/uuid';
  import CustomerRegItemGroupStatus from '@/views/summary/components/CustomerRegItemGroupStatus.vue';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import ReportVue from '@/components/Report/ReportVue.vue';
  import HealthQuestAddModal from '@/views/quest/components/HealthQuestAddModal.vue';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import AdviceEditor from '@/views/summary/components/AdviceEditor.vue';
  import DiagnosisEditor from '@/components/DiagnosisEditor/index.vue';
  import AbnormalSummaryEditor3 from '@/views/summary/components/AbnormalSummaryEditor3.vue';
  import AdviceEditor3 from '@/views/summary/components/AdviceEditor3.vue';

  const userStore = useUserStore();
  const user = userStore.getUserInfo;

  const showRegList = ref(true);

  const { hasPermission } = usePermission();
  const { token } = theme.useToken();

  const { createConfirm, createErrorModal } = useMessage();
  const { useToken } = theme;

  const editorModal = ref();
  const summaryPannelKey = ref('');
  const summaryTabKey = ref('diagnosis');
  const healthQuestAddModal = ref(null);

  async function handleSummaryPannelTab(key) {
    summaryPannelKey.value = key;
  }

  function handleSummaryTabChange(key) {
    summaryTabKey.value = key;
  }

  const btnLoading = ref(false);

  /**体检人员列表部分*/
  const currentDepartments = ref('');
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>(null);

  const customerReg2Provide = ref();
  provide('customerReg4Summary', customerReg2Provide);
  /**科室汇总部分*/
  const originDepartGroupList = ref([]);
  const departSummaryLoading = ref(false);
  const departSummary = ref('');
  const abnormalSummaries = ref<any[]>([]);
  const abnormalSummaryEditorRef = ref<any>(null);
  const adviceEditorRef = ref<any>(null);

  //诊断列表
  const diagnosisList = ref<string[]>([]);
  function handleDiagnosChange(list) {
    //diagnosisList.value = list;
  }

  function setDepartSummaryText(text) {
    departSummary.value = text;
  }

  function handleAuditOk(data) {
    if (data) {
      customerRegList.value?.reloadCurrent();
    }
  }

  function getAbnormalSummary() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }

    departSummaryLoading.value = true;
    // 简化逻辑，恢复正常显示
    listAbnormalSummaryByRegV2({ customerRegId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          console.log('获取异常汇总数据响应:', res);

          abnormalSummaries.value = res.result;

          // 尝试更新编辑器内容
          if (abnormalSummaryEditorRef.value) {
            nextTick(() => {
              try {
                if (abnormalSummaries.value.length > 0) {
                  // 有数据时加载数据
                  abnormalSummaryEditorRef.value.loadAbnormalSummaries(abnormalSummaries.value);
                } else {
                  // 无数据时清空编辑器
                  abnormalSummaryEditorRef.value.clearContent();
                }
              } catch (error) {
                console.error('更新编辑器内容时发生错误:', error);
              }
            });
          }
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        departSummaryLoading.value = false;
      });
  }

  const handleAddAdvice = (advice) => {
    console.log('SummaryPannel: Adding advice:', advice);
    // 触发AdviceEditor的addAdviceFromAbnormal方法
    if (adviceEditorRef.value) {
      adviceEditorRef.value.addAdviceFromAbnormal(advice);
    }
  };

  // 处理AI建议生成请求
  const handleGetAiSummary = async () => {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }

    try {
      // 获取异常汇总编辑器的内容
      let abnormalContent = [];

      if (abnormalSummaryEditorRef.value) {
        // 获取结构化的异常汇总数据
        abnormalContent = await abnormalSummaryEditorRef.value.getStructuredContent();
        // 触发AdviceEditor的getAiSummary方法
        if (adviceEditorRef.value) {
          await adviceEditorRef.value.getAiSummary(abnormalContent);
        }
      }
    } catch (error) {
      console.error('处理AI建议生成请求失败:', error);
      message.error('生成AI建议失败，请重试');
    }
  };

  /***健康证*/
  const healthCardForm = reactive<Record<string, any>>({
    healthCardResult: '合格',
  });
  const useForm = Form.useForm;

  async function saveHealthCardSummary() {
    if (customerSummary.value?.healthCardResult == null) {
      message.error('请选择健康证结果');
      return;
    }
    let data = {
      customerRegId: currentReg.value.id,
      summaryId: customerSummary.value.id,
      healthCardResult: customerSummary.value.healthCardResult,
    };
    await saveHealthCardResult(data);
  }

  /**项目组合部分*/
  const itemGroupStatus = ref();
  const departGroupList = ref([]);

  function handleItemGroupStatus(data) {
    departGroupList.value = data;
    originDepartGroupList.value = data;
  }

  /**复查项目部分*/
  const recheckTotal = ref(0);
  const recheckNotifyPannelModal = ref();

  function countRecheck() {
    if (!currentReg.value?.id) {
      return;
    }
    countByCustomerRegId({ customerRegId: currentReg.value.id }).then((res) => {
      recheckTotal.value = res;
    });
  }

  function openRecheckNotifyPannel() {
    if (!currentReg.value?.id) {
      message.warn('请先选择体检人员');
      return;
    }
    if (!customerSummary.value?.id) {
      message.error('请先保存总检结果');
      return;
    }
    recheckNotifyPannelModal.value?.open(currentReg.value.id, customerSummary.value.id);
  }

  async function handleQuestOk(personalQuestId) {
    await updateHealthQuestId({ id: currentReg.value.id, personalQuestId: personalQuestId });
    customerRegList.value?.reloadCurrent();
  }

  /**总检建议*/
  const aiSummary = ref(true);
  const historyResultReportId = ref();
  const abnormalDepartSummary = ref(true);
  const customerSummary = ref<CustomerRegSummary>({});
  provide('customerSummary', customerSummary);
  const adviceKeywords = ref('');
  const adviceList = ref<AdviceBean[]>([]);

  const summaryAdviceLoading = ref(false);
  const adviceSpinText = ref('正在获取建议');

  function getSettingFromLocalStorage(key: string, defaultValue: boolean): boolean {
    const value = localStorage.getItem(key);
    return value !== null ? JSON.parse(value) : defaultValue;
  }

  function confirmReplaceAdvice() {
    return new Promise<void>((resolve, reject) => {
      if (adviceList.value.length == 0) {
        resolve();
      } else {
        createConfirm({
          iconType: 'warning',
          title: '获取自动建议提示',
          content: '当前建议列表不为空，是否覆盖？',
          onOk: () => {
            resolve();
          },
          onCancel: () => {
            reject();
          },
        });
      }
    });
  }

  function adoptAdvice(advice) {
    console.log('SummaryPannel: Adopting advice from knowledge base:', advice);
    // 使用AdviceEditor3的addAdviceFromKnowledgeBase方法
    if (adviceEditorRef.value && summaryTabKey === 'advice') {
      adviceEditorRef.value.addAdviceFromKnowledgeBase(advice);
    } else {
      // 如果当前不在建议面板或组件未加载，使用原来的方式
      // 找到第一个选中的建议项
      let selectedIndex = adviceList.value.findIndex((item) => item.chk === true);

      // 如果没有选中项，检查是否有空内容的项
      if (selectedIndex === -1) {
        selectedIndex = adviceList.value.findIndex((item) => !item.content || item.content.trim() === '');
      }

      // 如果找到选中项或空内容项
      if (selectedIndex !== -1) {
        adviceList.value[selectedIndex].name = advice.keywords;
        adviceList.value[selectedIndex].content = advice.adviceContent;
        adviceList.value[selectedIndex].source = '知识库';
      } else {
        // 如果没有选中项或空内容项，添加新建议
        adviceList.value.push({
          seq: adviceList.value.length + 1,
          name: advice.keywords,
          content: advice.adviceContent,
          chk: false,
          source: '知识库',
          key: buildUUID(),
        });
      }

      triggerAutoSave();

      // 如果当前不在建议面板，切换到建议面板
      if (summaryTabKey !== 'advice') {
        summaryTabKey = 'advice';
      }
    }
  }

  let handleEditAdviceName = _.debounce((e) => {
    adviceKeywords.value = e.target.value;
  }, 100);

  function handleMouseUp(event) {
    const textArea = event.target;
    const start = textArea.selectionStart;
    const end = textArea.selectionEnd;
    adviceKeywords.value = textArea.value.substring(start, end);
  }

  function checkAllMajorItemsSummarized(): string[] {
    return originDepartGroupList.value.filter((departGroup) => !departGroup.summaryFlag).map((departGroup) => departGroup.depart.departName);
  }

  async function saveOrUpdateSummary(type: string = '') {
    //校验
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (type == 'normal' && !customerSummary.value.preAuditor) {
      message.warn('请先保存初检！');
      return;
    }
    /* if (departSummary.value == '') {
message.warn('请填写汇总');
return;
}*/
    /* if (adviceList.value.length == 0) {
message.warn('请填写建议');
return;
}*/
    const unsummarizedItems = checkAllMajorItemsSummarized();
    if (unsummarizedItems.length > 0) {
      createConfirm({
        iconType: 'warning',
        title: '确认总检提示',
        content: `以下科室未填写小结：${unsummarizedItems.join(', ')}，是否继续保存？`,
        onOk: async () => {
          await performSave(type);
        },
      });
    } else {
      await performSave(type);
    }
  }

  async function performSave(type) {
    let summaryJson = adviceList.value.map((item, index) => ({
      seq: index + 1,
      name: item.name,
      content: item.content,
      source: item.source,
      markdown: true, // 标记内容为 markdown 格式
    }));

    // 生成传统的建议文本，保持向后兼容
    let advice = adviceList.value
      .map((item) => {
        // 移除 markdown 标记，提取纯文本内容
        const plainText = item.content
          .replace(/\*\*(.*?)\*\*/g, '$1') // 移除加粗标记
          .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
          .replace(/^# (.*?)$/gm, '$1') // 移除 H1
          .replace(/^## (.*?)$/gm, '$1') // 移除 H2
          .replace(/^### (.*?)$/gm, '$1'); // 移除 H3

        return plainText;
      })
      .join('\r\n');

    let postData = {
      id: customerSummary.value.id,
      characterSummary: departSummary.value,
      summaryJson: summaryJson,
      advice: advice,
      type: type,
      customerRegId: currentReg.value.id,
      examNo: currentReg.value.examNo,
    };

    try {
      await saveSummary(postData);
      customerRegList.value?.reloadCurrent();
    } catch (error) {
      console.error(error);
    }
  }

  const summaryAuditRecordModal = ref(null);
  const summaryAuditRecordListModal = ref(null);
  const auditRecordList = ref([]);
  const showAuditTip = ref(false);
  const auditRecord = ref(null);

  function getAditRecord(summaryId) {
    listAditRecord({ summaryId: summaryId }).then((res) => {
      auditRecordList.value = res.records;
      if (auditRecordList.value.length > 0 && auditRecordList.value[0].auditResult == '驳回') {
        showAuditTip.value = true;
        auditRecord.value = auditRecordList.value[0];
      }
    });
  }

  function openAuditModal() {
    if (!customerSummary.value.id) {
      message.warn('请先保存总检信息！');
      return;
    }
    if (!currentReg.value.id) {
      message.warn('请选择体检人员！');
      return;
    }
    if (!customerSummary.value.preAuditor) {
      message.warn('尚未初检，不能审核！');
      return;
    }
    if (!customerSummary.value.creatorName) {
      message.warn('尚未主检，不能审核！');
      return;
    }
    summaryAuditRecordModal.value?.add(customerSummary.value, true);
  }

  function openAuditRecordModal() {
    summaryAuditRecordListModal.value?.open(customerSummary.value.id);
  }

  function revokeAudit() {
    if (user.username !== customerSummary.value.auditeBy) {
      message.error('只有审核者本人才能撤销审核');
      return;
    }
    //检查是否已审核
    createConfirm({
      iconType: 'warning',
      title: '撤销审核提示',
      content: '确定要撤销审核吗？',
      onOk: () => {
        let data = {
          summaryId: customerSummary.value.id,
          customerRegId: currentReg.value.id,
        };
        revokeSummaryStatus(data).then((res) => {
          //customerSummary.value.status = res;
          //currentReg.value.summaryStatus = res;
          customerRegList.value?.reloadCurrent();
          //message.success('撤销审核成功！');
        });
      },
    });
  }

  /**科室结果图片*/
  const currentItemResult = ref<ICustomerRegItemResult>({});
  const priewPic = (urls) => {
    preview({
      images: urls.map((item) => getFileAccessHttpUrl(item)),
    });
  };

  const printerSetupModalRef = ref(null);

  function handleMenuClick(menu) {
    //console.log('handleMenuClick', menu);
    if (menu.key === 'allPic') {
      previewAllPic();
    } else if (menu.key === 'occuHistory') {
      openInquiry();
    } else if (menu.key === 'printerSetup') {
      openPrinterSetupModal();
    } else if (menu.key === 'printApply') {
      printCurrentGuide();
    } else if (menu.key === 'hisData') {
      openDataModal();
    } else if (menu.key === 'fixLisData') {
      fixLisData();
    } else if (menu.key === 'fixCheckData') {
      fixCheckData();
    } else if (menu.key === 'navLis') {
      navLis();
    } else if (menu.key === 'navCheck') {
      navPacs();
    } else if (menu.key === 'healthQuest') {
      healthQuest();
    }
  }

  /**打印导引单*/
  async function printCurrentGuide() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录');
      return;
    }
    printGuide(currentReg.value);
    //printGuide(currentReg.value);
  }

  function getGuideTemplate() {
    return new Promise((resolve, reject) => {
      getDefaultIdOfType({ type: '导引单' }).then((res) => {
        if (res.success) {
          let templateId = res.result;
          getTemplateById({ id: templateId }).then((templateRes) => {
            resolve(JSON.parse(templateRes.content));
          });
        }
      });
    });
  }

  async function printGuide(reg) {
    let regUnref = unref(reg);
    if (!regUnref) {
      return;
    }
    try {
      const customerRegDetail = await getGuidanceSheet({ id: regUnref.id });
      if (customerRegDetail.reg.avatar) {
        customerRegDetail.reg.avatar = getFileAccessHttpUrl(customerRegDetail.reg.avatar);
      }
      let template = await getGuideTemplate();
      if (!template) {
        message.error('未找到导引单模板');
        return;
      }
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(customerRegDetail);
      await printReportDirect(template, PrinterType.Guide);
      updateGuidancePrintTimes({ regId: regUnref.id });
    } catch (e) {
      console.log(e);
    }
  }

  function openPrinterSetupModal() {
    printerSetupModalRef.value?.open();
  }

  /**第三方接口url*/
  const lisUrl = ref('');
  const checkUrl = ref('');

  function fetchUrlSetting() {
    querySysParamByCode({ code: 'lis_url' }).then((res) => {
      lisUrl.value = res.result;
    });
    querySysParamByCode({ code: 'check_url' }).then((res) => {
      checkUrl.value = res.result;
    });
  }

  const navLis = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!lisUrl.value) {
      message.error('未配置LIS系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    // Replace {regId} in the URL with the actual regId
    const fullUrl = lisUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const navPacs = () => {
    if (!currentReg.value.examNo) {
      message.error('请选择体检记录！');
      return;
    }
    if (!checkUrl.value) {
      message.error('未配置检查系统地址！');
      return;
    }
    let regId = currentReg.value.id;
    const fullUrl = checkUrl.value.replace('{regId}', regId);
    window.open(fullUrl, '_blank');
  };

  const previewAllPic2 = () => {
    const picSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (group.reportPics && group.reportPics.length > 0) {
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });

    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };
  const previewAllPic = () => {
    const picSet = new Set<string>();
    const reportPdfInterfaceSet = new Set<string>();
    departGroupList.value.forEach((depart) => {
      depart.groupList.forEach((group) => {
        if (!group.reportPdfInterface) {
          // 处理 reportPdfInterface 为 null 的情况
          if (group.reportPics && group.reportPics.length > 0) {
            group.reportPics.forEach((pic) => {
              picSet.add(pic);
            });
            // 注意：这里我们不需要再为 null 创建一个特殊的标记，
            // 因为我们通过检查 reportPdfInterface 的值来隐式地处理了这种情况。
          }
        } else if (!reportPdfInterfaceSet.has(group.reportPdfInterface) && group.reportPics && group.reportPics.length > 0) {
          // 处理非 null 和非空的 reportPdfInterface
          reportPdfInterfaceSet.add(group.reportPdfInterface);
          group.reportPics.forEach((pic) => picSet.add(pic));
        }
      });
    });
    const uniquePicList = Array.from(picSet);
    if (uniquePicList.length > 0) {
      preview({
        images: uniquePicList.map((item) => getFileAccessHttpUrl(item)),
      });
    } else {
      message.info('没有图片!');
    }
  };

  function healthQuest() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    healthQuestAddModal.value?.open(currentReg.value);
  }

  const registerUploadModal = ref(null);
  const fileList = ref<string[]>([]);

  function handlePicChange(urls: string[]) {
    //找出新增或删除的图片
    let addList = urls.filter((item) => !fileList.value.includes(item));
    let delList = fileList.value.filter((item) => !urls.includes(item));
    if (addList.length > 0 || delList.length > 0) {
      //更新对应项目的图片
      currentItemResult.value.pic = urls;
      saveItemResult({ resultList: [currentItemResult.value] });
    }
  }

  /**职业病问诊*/
  const inquiryModal = ref(null);

  function openInquiry() {
    inquiryModal.value?.open(currentReg.value, true);
  }

  const jimuReportModal = ref();

  function openDataModal() {
    jimuReportModal.value?.open({ archivesNum: currentReg.value.archivesNum });
  }

  function fixLisData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchLisData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检验数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检验数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  function fixCheckData() {
    if (!currentReg.value.id) {
      message.error('请选择体检记录！');
      return;
    }
    btnLoading.value = true;
    fetchCheckData({ regId: currentReg.value.id })
      .then((res) => {
        if (res.success) {
          message.success('检查数据数据更新成功！');
          customerRegList.value?.reloadCurrent();
        } else {
          message.error('检查数据数据更新失败！');
        }
      })
      .finally((e) => {
        btnLoading.value = false;
      });
  }

  /**科室提醒抽屉部分*/
  const departmentTipCount = ref(0);
  const departTipRef = ref(null);

  function openDepartTipDrawer() {
    departTipRef.value?.open();
  }

  function handleDepartTipDrawerLoaded(count: number) {
    departmentTipCount.value = count;
  }

  /**危急值提示部分*/
  function showCriticalItems(criticalItems: CustomerRegCriticalItem[], refresh: boolean = false) {
    if (criticalItems.length == 0) {
      return;
    }
    // 根据severityDegree分类（A类和B类）
    const categoryA = criticalItems.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.filter((item) => item.severityDegree === 'B类');

    // 然后据此拼接提示信息
    const messageA = categoryA.map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`).join(' <br/>');
    const messageB = categoryB.map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`).join(' <br/>');

    let title = '';
    if (categoryA.length > 0) {
      title += `发现${categoryA.length}项A类危急值 `;
    }
    if (categoryB.length > 0) {
      title += `${categoryB.length}项B类危急值`;
    }

    let message = '<div style="max-height: 50vh;overflow-y: auto;">';
    if (categoryA.length > 0) {
      message += `<span style="color:#f5222d;font-weight: normal">A类（需要立即进行临床干预，否则
将危及生命或导致严重不良后果的异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageA}</p><br/>`;
    }
    if (categoryB.length > 0) {
      message += `<span style="color:#faad14;font-weight: bold;">B类（需要临床进一步检查以明确诊断和(或)需要
医学治疗疗的重要异常结果）</span><br/><p style="font-weight: bold;color:#000000">${messageB}</p>`;
    }
    message += '</div>';

    // 显示提示信息
    createErrorModal({
      title: title,
      content: message,
      iconType: 'warning',
      onOk: () => {
        if (refresh) {
          getCriticalItemByRegId();
        }
      },
    });
  }

  const criticalItems = ref<CustomerRegCriticalItem[]>([]);

  function getCriticalItemByRegId(showTip: boolean = false) {
    getCriticalItem({ regId: currentReg.value.id }).then((res) => {
      if (showTip) {
        showCriticalItems(res, false);
      }
      criticalItems.value = res;
    });
  }

  const criticalTip = computed(() => {
    let tip = '';

    if (criticalItems.value.length == 0) {
      return tip;
    }
    const categoryA = criticalItems.value.filter((item) => item.severityDegree === 'A类');
    const categoryB = criticalItems.value.filter((item) => item.severityDegree === 'B类');

    const messageA = categoryA.map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`).join('；');
    const messageB = categoryB.map((item, index) => `${index + 1}. ${item.itemName}${item.checkPartName ? `（${item.checkPartName}）` : ''}`).join('；');

    if (categoryA.length > 0) {
      tip += `发现${categoryA.length}项A类危急值：${messageA}；`;
    }
    if (categoryB.length > 0) {
      tip += `发现${categoryB.length}项B类危急值：${messageB}；`;
    }

    return tip;
  });

  function handleRegTableRowClick(selectedRow) {
    // 清除之前的加载状态
    summaryAdviceLoading.value = false;
    departSummaryLoading.value = false;

    // 更新当前患者信息
    currentReg.value = selectedRow;
    customerReg2Provide.value = selectedRow;

    if (selectedRow.id) {
      //open.value = false;
      showRegList.value = false;
      if (currentReg.value.examCategory == '职业病体检') {
        summaryPannelKey.value = 'occu';
      } else if (currentReg.value.examCategory == '健康证体检') {
        summaryPannelKey.value = 'healthCard';
      } else {
        summaryPannelKey.value = 'health';
      }

      // 重置数据
      adviceList.value = [];
      departSummary.value = '';

      // 获取新数据
      fetchData();
    }
  }

  function returnToList() {
    // 清空当前选中的记录
    currentReg.value = null;
    customerReg2Provide.value = null;

    // 显示列表
    showRegList.value = true;
    summaryPannelKey.value = 'health';
  }

  function fetchData() {
    auditRecordList.value = [];
    showAuditTip.value = false;
    auditRecord.value = null;

    if (currentReg.value.id) {
      getCriticalItemByRegId(true);
      getAbnormalSummary();

      //getSummaryAdvice();
      countRecheck();
      filterTemplateByReg();
      nextTick(() => {
        itemGroupStatus.value?.loadData(currentReg.value.id, 'abnormal');
      });
    }
  }

  /**报告预览打印*/
  const reportRef = ref(null);
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const currentReportTemplateId = ref(null);
  const reportDataLoading = ref(false);

  async function previewReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    //获取报告模版内容
    reportDataLoading.value = true;
    try {
      const templateRes = await getTemplateById({ id: currentReportTemplateId.value });
      let template = JSON.parse(templateRes.content);
      const reportDataRes = await getReportData({ customerRegId: currentReg.value.id });
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;
        //需要处理报告中的图片
        //console.log(reportData);
        if (reportData.customerReg.avatar) {
          reportData.customerReg.avatar = getFileAccessHttpUrl(reportData.customerReg.avatar);
        }
        if (reportData.reportImgList?.length > 0) {
          reportData.reportImgList?.forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        }
        //console.log(reportData.groupByFunctionMap?.lab_exam);

        if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap) {
          Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
            reportData.groupByFunctionPicMap[key].forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          });
        }

        //console.log(reportData.groupByFunctionMap.lab_exam);
        if (reportData.summaryAdvice?.auditorSignPic) {
          reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
        }
        if (reportData.summaryAdvice?.preAuditorSignPic) {
          reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
        }
        if (reportData.summaryAdvice?.creatorSignPic) {
          reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
        }
        console.log('=======reportData==========', reportData);
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
        if (!reportRef.value) {
          message.error('报告组件初始化失败！');
          return;
        }
        reportRef.value.open({
          filename: `${currentReg.value.name}的体检报告`,
          template: template,
        });
      } else {
        message.error('获取报告数据失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      reportDataLoading.value = false;
    }
  }

  async function printReport() {
    if (!currentReg.value.id) {
      message.warn('请选择体检人员');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    if (customerSummary.value.status != '审核通过') {
      message.warn('总检未审核，不可以打印报告！');
      return;
    }

    previewReport();

    await updateReportPrintTimes({ id: customerSummary.value.id });
  }

  function fetchReportTemplateList() {
    getReportTemplate({ type: '报告' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
        reportTemplateList.value = res.result.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  }

  function filterTemplateByReg() {
    let reg = currentReg.value;
    if (!reg.id) {
      return;
    }
    let examCategory = reg.examCategory;
    let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == examCategory);
    currentReportTemplateId.value = regTemplateList[0]?.id;
  }

  function handleHistoryResultReportId() {
    querySysParamByCode({ code: 'historyResultReportId' }).then((res) => {
      historyResultReportId.value = res.result;
    });
  }

  // Update onMounted to include cleanup on component unmount
  onMounted(() => {
    aiSummary.value = getSettingFromLocalStorage('aiSummary', true);
    abnormalDepartSummary.value = getSettingFromLocalStorage('abnormalDepartSummary', true);
    handleHistoryResultReportId();
    fetchReportTemplateList();
    fetchUrlSetting();
  });

  // Add beforeUnmount hook
  onBeforeUnmount(() => {});
</script>
<style scoped>
  .abandon {
    text-decoration: line-through #ff4d4f;
  }

  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }

  .error-modal {
    max-height: 50vh;
    overflow-y: scroll;
  }

  .chosenClass {
    opacity: 1;
    border-style: solid;
    border-width: 1px;
    border-color: v-bind('token.colorPrimary');
  }

  .ghost {
    border: solid 1px v-bind('token.colorPrimary') !important;
  }

  .drag-handle {
    cursor: move; /* Change the cursor to a 'move' icon when hovering over the handle */
  }

  .current-advice {
    border: 1px solid v-bind('token.colorPrimary');
    border-radius: 2px;
  }

  :deep(.ant-card-body) {
    padding: 6px;
  }

  :deep(.ant-collapse-header) {
    padding: 2px 8px;
  }

  .fade-move,
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: scaleY(0.01) translate(30px, 0);
  }

  .fade-leave-active {
    position: absolute;
  }

  .highlight {
    background-color: #f0f0f0;
    transition: background-color 0.3s ease;
  }

  .hint-bar {
    position: fixed;
    top: 95%;
    left: 0;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 0 5px 5px 0;
    z-index: 1000;
  }

  .chosen {
    background-color: #e0e0e0;
  }

  .drag {
    background-color: #c0c0c0;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
    width: 14px;
    font-size: 12px;
    letter-spacing: normal;
    line-height: 1;
    padding: 3px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .left-pane {
    background-color: #f0f0f0;
    padding: 20px;
  }

  .vertical-tab-text {
    /* 覆盖Ant Design Vue的默认样式 */

    .ant-tabs-nav {
      width: 18px !important;
      min-width: 18px !important;
    }

    .ant-tabs-tab {
      padding: 2px 0 !important;
      margin: 0 0 2px 0 !important;
      height: auto !important;
      min-height: 40px !important;
    }

    .ant-tabs-tab-btn {
      padding: 0 !important;
    }

    .ant-tabs-content-holder {
      border-left: none !important;
    }

    .ant-tabs-ink-bar {
      width: 1px !important;
    }

    .ant-tabs-tab-active {
      background-color: #f0f0f0 !important;
    }

    .ant-tabs-tabpane {
      padding-right: 2px !important;
    }

    /* 右侧标签页特定样式 */

    &.ant-tabs-right {
      .ant-tabs-nav {
        width: 18px !important;
      }

      .ant-tabs-nav-list {
        width: 18px !important;
      }

      .ant-tabs-tab {
        width: 18px !important;
        justify-content: center !important;
        text-align: center !important;
      }
    }
  }

  .right-pane {
    background-color: #e0e0e0;
    padding: 20px;
  }

  /* 确保a-tabs内容区域充分利用空间 */
  .ant-tabs-content {
    height: 100%;
  }

  .ant-tabs-tabpane {
    height: 100%;
    overflow: hidden;
  }

  /* 体检信息单行布局 */
  .exam-info-single-row {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 6px 8px;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 6px;
    font-size: 13px;
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    height: 36px;
  }

  .info-label {
    color: rgba(0, 0, 0, 0.65);
    margin-right: 2px;
    white-space: nowrap;
    font-size: 13px;
  }

  .info-value {
    color: rgba(0, 0, 0, 0.85);
    margin-right: 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 150px;
    font-size: 13px;
  }

  .info-value.strong {
    font-weight: bold;
  }

  .exam-no {
    margin-left: 4px;
    margin-right: 4px;
  }

  .info-divider {
    color: #d9d9d9;
    margin: 0 4px;
  }

  /* 标签样式调整 */
  .exam-info-single-row .ant-tag {
    margin-right: 4px;
    line-height: 18px;
    height: 20px;
    padding: 0 5px;
    font-size: 12px;
  }

  .bottom-bar {
    width: 100%;
    padding: 10px;
    background-color: #ffffff;
  }

  /* 底部操作栏样式 */
  .bottom-action-bar {
    position: relative;
    background-color: #fff;
    padding: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 2px;
    margin-right: 40px;
  }

  .action-bar-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .left-actions {
    display: flex;
    align-items: center;
  }

  .right-actions {
    display: flex;
    align-items: center;
  }

  /* 返回列表图标按钮样式 */
  .return-list-icon {
    margin-left: auto;
    padding: 0 8px;
    height: 28px;
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  .return-list-icon .anticon {
    margin-right: 4px;
  }
</style>
