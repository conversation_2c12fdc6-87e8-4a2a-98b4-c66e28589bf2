# API降级处理修复说明

## 🔧 问题描述

DiseaseAutoComplete组件在加载热门选项时出现404错误：
```
❌ 加载热门选项失败: AxiosError {message: 'Request failed with status code 404'}
```

**原因**: 后端尚未实现新的ICD10集成接口：
- `/system/questionnaireCommonOptions/searchWithIcd`
- `/system/questionnaireCommonOptions/popularWithIcd`

## ✅ 已实现的修复

### 1. 智能降级机制 ✅

**热门选项加载降级**:
```typescript
// 首先尝试新的集成ICD10的接口
let response;
let useIcdIntegration = false;

try {
  response = await defHttp.get({
    url: '/system/questionnaireCommonOptions/popularWithIcd',
    params: { fieldName: props.fieldName, limit: props.limit },
  }, { isTransformResponse: false });
  
  useIcdIntegration = true;
  console.log('📡 热门选项响应（含ICD10）:', response);
} catch (icdError) {
  console.warn('⚠️ ICD10集成接口不可用，降级到原接口:', icdError.message);
  
  // 降级到原来的接口
  response = await defHttp.get({
    url: '/system/questionnaireCommonOptions/popular',
    params: { fieldName: props.fieldName, limit: props.limit },
  }, { isTransformResponse: false });
  
  console.log('📡 热门选项响应（原接口）:', response);
}
```

**搜索功能降级**:
```typescript
try {
  response = await defHttp.get({
    url: '/system/questionnaireCommonOptions/searchWithIcd',
    params: { fieldName: props.fieldName, keyword: keyword, limit: props.limit },
  }, { isTransformResponse: false });
  
  useIcdIntegration = true;
} catch (icdError) {
  // 降级到原始搜索接口
  response = await defHttp.get({
    url: '/system/questionnaireCommonOptions/search',
    params: { fieldName: props.fieldName, keyword: keyword, limit: props.limit },
  }, { isTransformResponse: false });
}
```

### 2. 响应格式适配 ✅

**ICD10集成接口响应格式**:
```json
{
  "success": true,
  "result": {
    "dictData": [
      {
        "optionText": "高血压",
        "icdCode": "I10",
        "useCount": 50,
        "optionValue": "gxy"
      }
    ],
    "icdData": [
      {
        "name": "原发性高血压",
        "code": "I10",
        "icdName": "原发性高血压",
        "icdCode": "I10"
      }
    ]
  }
}
```

**原始接口响应格式**:
```json
{
  "success": true,
  "result": [
    {
      "optionText": "高血压",
      "icdCode": "I10",
      "useCount": 50,
      "optionValue": "gxy"
    }
  ]
}
```

### 3. 数据处理逻辑 ✅

**根据接口类型处理数据**:
```typescript
if (useIcdIntegration) {
  // 处理ICD10集成接口的响应
  if (response.result.dictData) {
    // 处理字典数据
    response.result.dictData.forEach((item: any) => {
      mappedOptions.push({
        value: item.optionText,
        label: item.optionText,
        source: 'dict',
        isIcd: false,
        // ...其他字段
      });
    });
  }
  
  if (response.result.icdData) {
    // 处理ICD10数据
    response.result.icdData.forEach((item: any) => {
      mappedOptions.push({
        value: item.name || item.icdName,
        label: `${item.name || item.icdName} (${item.code || item.icdCode})`,
        source: 'icd10',
        isIcd: true,
        // ...其他字段
      });
    });
  }
} else {
  // 处理原始接口的响应（只有字典数据）
  if (Array.isArray(response.result)) {
    response.result.forEach((item: any) => {
      mappedOptions.push({
        value: item.optionText,
        label: item.optionText,
        source: 'dict',
        isIcd: false,
        // ...其他字段
      });
    });
  }
}
```

## 🎯 降级策略

### 接口降级顺序
1. **优先**: 尝试ICD10集成接口（含字典+ICD10数据）
2. **降级**: 使用原始接口（仅字典数据）
3. **兜底**: 使用硬编码的默认选项

### 功能保持
- ✅ **基本搜索**: 原始接口完全支持
- ✅ **热门选项**: 原始接口完全支持
- ✅ **自动新增**: 不受影响
- ⚠️ **ICD10数据**: 仅在新接口可用时提供
- ✅ **使用频次**: 原始接口完全支持

## 🔍 调试信息

**成功使用ICD10集成接口**:
```
🔥 开始加载热门选项
🌐 尝试ICD10集成接口: {...}
📡 热门选项响应（含ICD10）: {...}
🔄 处理ICD10集成接口响应
✅ 热门选项映射: [...]
```

**降级到原始接口**:
```
🔥 开始加载热门选项
🌐 尝试ICD10集成接口: {...}
⚠️ ICD10集成接口不可用，降级到原接口: Request failed with status code 404
🌐 使用原始接口: {...}
📡 热门选项响应（原接口）: {...}
🔄 处理原始接口响应
✅ 热门选项映射: [...]
```

## 🚀 部署策略

### 阶段1：当前状态（原始接口）
- ✅ 组件正常工作
- ✅ 基本功能完整
- ⚠️ 无ICD10数据

### 阶段2：后端实现新接口
- 🔄 后端实现 `searchWithIcd` 接口
- 🔄 后端实现 `popularWithIcd` 接口
- ✅ 前端自动使用新接口
- ✅ 获得ICD10数据支持

### 阶段3：完全迁移（可选）
- 🔄 移除降级逻辑
- 🔄 只使用新接口
- ✅ 代码简化

## 📋 后端接口实现建议

### searchWithIcd 接口
```java
@GetMapping("/searchWithIcd")
public Result<Map<String, List<Object>>> searchWithIcd(
    @RequestParam String fieldName,
    @RequestParam String keyword,
    @RequestParam(defaultValue = "10") Integer limit
) {
    // 搜索字典数据
    List<QuestionnaireCommonOptions> dictData = searchDictData(fieldName, keyword, limit);
    
    // 搜索ICD10数据
    List<IcdData> icdData = searchIcdData(keyword, limit);
    
    Map<String, List<Object>> result = new HashMap<>();
    result.put("dictData", dictData);
    result.put("icdData", icdData);
    
    return Result.ok(result);
}
```

### popularWithIcd 接口
```java
@GetMapping("/popularWithIcd")
public Result<Map<String, List<Object>>> popularWithIcd(
    @RequestParam String fieldName,
    @RequestParam(defaultValue = "10") Integer limit
) {
    // 获取热门字典数据
    List<QuestionnaireCommonOptions> dictData = getPopularDictData(fieldName, limit);
    
    // 获取热门ICD10数据
    List<IcdData> icdData = getPopularIcdData(limit);
    
    Map<String, List<Object>> result = new HashMap<>();
    result.put("dictData", dictData);
    result.put("icdData", icdData);
    
    return Result.ok(result);
}
```

## 🎉 修复效果

现在组件可以：
- ✅ 正常加载和显示热门选项
- ✅ 正常搜索疾病数据
- ✅ 自动降级到可用接口
- ✅ 提供详细的调试信息
- ✅ 为未来的ICD10集成做好准备

用户不会再看到404错误，组件功能完全正常！🚀
