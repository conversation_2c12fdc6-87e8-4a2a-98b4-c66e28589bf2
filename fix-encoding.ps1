# 修复CustomerRegGroupPannel.vue中的中文乱码
$filePath = "src\views\reg\components\CustomerRegGroupPannel.vue"

# 读取文件内容
$content = Get-Content $filePath -Encoding UTF8 -Raw

# 修复常见的中文乱码
$content = $content -replace '<!--操作.*?-->', '<!--操作栏-->'
$content = $content -replace '<!--字段回显插槽-->', '<!--字段回显插槽-->'
$content = $content -replace '<!--表格字段插槽-->', '<!--表格字段插槽-->'
$content = $content -replace '鍗曚綅棰勭害浜哄憳涓嶅彲浣跨敤濂楅', '单位预约人员不可使用套餐'
$content = $content -replace '椤圭洰锟?', '项目'
$content = $content -replace '�', ''

# 保存文件
Set-Content $filePath -Value $content -Encoding UTF8 -NoNewline

Write-Host "文件编码修复完成"
