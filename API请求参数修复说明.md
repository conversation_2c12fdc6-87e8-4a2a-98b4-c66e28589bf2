# API请求参数修复说明

## 🔧 修复内容

根据项目规范，所有API请求都需要添加 `{ isTransformResponse: false }` 参数以确保数据解析逻辑正确匹配。

## ✅ 已修复的文件

### 1. questionnaireCommonOptions.api.ts
修复了以下API调用：

```typescript
// 修复前
export const getQuestionnaireCommonOptionsList = (params) => defHttp.get({ url: Api.list, params });

// 修复后
export const getQuestionnaireCommonOptionsList = (params) => defHttp.get({ url: Api.list, params }, { isTransformResponse: false });
```

**修复的API方法**:
- `getQuestionnaireCommonOptionsList` - 分页列表查询
- `searchQuestionnaireOptions` - 搜索选项
- `getPopularOptions` - 获取热门选项
- `recordOptionUsage` - 记录选项使用
- `saveOrUpdateQuestionnaireOptions` - 保存或更新（PUT/POST）
- `exportXls` - 导出Excel

### 2. DiseaseAutoComplete.vue
修复了组件内所有直接的API调用：

```typescript
// 修复前
const response = await defHttp.get({
  url: '/system/questionnaireCommonOptions/search',
  params: { ... },
});

// 修复后
const response = await defHttp.get({
  url: '/system/questionnaireCommonOptions/search',
  params: { ... },
}, { isTransformResponse: false });
```

**修复的API调用位置**:
1. **搜索功能** - `debouncedSearch` 函数中的搜索请求
2. **热门选项** - `loadPopularOptions` 函数中的热门选项请求
3. **新增疾病** - `addNewDisease` 函数中的添加请求
4. **记录使用** - `recordUsageWithRetry` 函数中的使用记录请求
5. **自动检查** - `checkAndAddNewDisease` 函数中的检查请求

## 🎯 修复原因

项目使用了自定义的响应转换逻辑，需要通过 `{ isTransformResponse: false }` 参数来：

1. **保持原始响应格式** - 避免axios默认的响应转换
2. **匹配数据解析逻辑** - 确保组件中的数据解析能正确处理响应
3. **统一项目规范** - 与其他API文件保持一致的调用方式

## 📋 修复对比

### GET请求修复
```typescript
// 修复前
defHttp.get({ url: '/api/endpoint', params })

// 修复后  
defHttp.get({ url: '/api/endpoint', params }, { isTransformResponse: false })
```

### POST请求修复
```typescript
// 修复前
defHttp.post({ url: '/api/endpoint', data })

// 修复后
defHttp.post({ url: '/api/endpoint', data }, { isTransformResponse: false })
```

### PUT请求修复
```typescript
// 修复前
defHttp.put({ url: '/api/endpoint', params })

// 修复后
defHttp.put({ url: '/api/endpoint', params }, { isTransformResponse: false })
```

## 🔍 验证方法

修复后，组件的数据解析逻辑应该能正确处理API响应：

```typescript
// 验证响应格式
if (!validateApiResponse(response)) {
  console.error('❌ API响应格式无效:', response);
  return;
}

if (response.success && response.result) {
  // 正确处理响应数据
  const mappedOptions = response.result.map(item => ({
    value: item.optionText,
    label: item.optionText,
    // ...其他字段
  }));
}
```

## 🚀 影响范围

这个修复确保了：

1. **搜索功能正常** - 疾病搜索能正确显示结果
2. **热门选项加载** - 初始化时能正确加载热门疾病
3. **自动新增功能** - 新疾病能正确添加到数据库
4. **使用频次记录** - 选择疾病时能正确记录使用频次
5. **错误处理机制** - 数据验证和错误处理能正常工作

## 📝 注意事项

1. **统一规范** - 项目中所有API调用都应该使用 `{ isTransformResponse: false }`
2. **响应处理** - 确保组件中的响应处理逻辑与这个参数匹配
3. **错误处理** - 验证错误处理逻辑在新的响应格式下正常工作
4. **测试验证** - 建议测试所有相关功能确保修复有效

现在DiseaseAutoComplete组件的所有API调用都符合项目规范，应该能正确处理服务器响应了！
