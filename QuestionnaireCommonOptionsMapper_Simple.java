package org.jeecg.modules.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.system.entity.QuestionnaireCommonOptions;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @Description: 问卷常用选项字典
 * @Author: jeecg-boot
 * @Date: 2024-12-18
 * @Version: V1.0
 */
public interface QuestionnaireCommonOptionsMapper extends BaseMapper<QuestionnaireCommonOptions> {

    /**
     * 根据字段名和关键词搜索选项，按使用频次排序
     * @param fieldName 字段名称
     * @param keyword 搜索关键词
     * @param limit 限制返回数量
     * @return 选项列表
     */
    @Select("SELECT * FROM questionnaire_common_options " +
            "WHERE field_name = #{fieldName} " +
            "AND (option_text LIKE CONCAT('%', #{keyword}, '%') " +
            "OR pinyin LIKE CONCAT('%', #{keyword}, '%') " +
            "OR icd_code LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY use_count DESC, last_used_time DESC " +
            "LIMIT #{limit}")
    List<QuestionnaireCommonOptions> searchByFieldAndKeyword(@Param("fieldName") String fieldName, 
                                                            @Param("keyword") String keyword, 
                                                            @Param("limit") Integer limit);

    /**
     * 根据字段名获取热门选项，按使用频次排序
     * @param fieldName 字段名称
     * @param limit 限制返回数量
     * @return 选项列表
     */
    @Select("SELECT * FROM questionnaire_common_options " +
            "WHERE field_name = #{fieldName} " +
            "ORDER BY use_count DESC, last_used_time DESC " +
            "LIMIT #{limit}")
    List<QuestionnaireCommonOptions> getPopularOptions(@Param("fieldName") String fieldName, 
                                                      @Param("limit") Integer limit);

    /**
     * 增加使用频次
     * @param id 选项ID
     * @return 更新行数
     */
    @Update("UPDATE questionnaire_common_options " +
            "SET use_count = use_count + 1, last_used_time = NOW() " +
            "WHERE id = #{id}")
    int incrementUseCount(@Param("id") String id);

    /**
     * 根据字段名和选项值查找选项
     * @param fieldName 字段名称
     * @param optionValue 选项值
     * @return 选项
     */
    @Select("SELECT * FROM questionnaire_common_options " +
            "WHERE field_name = #{fieldName} AND option_value = #{optionValue}")
    QuestionnaireCommonOptions findByFieldAndValue(@Param("fieldName") String fieldName, 
                                                  @Param("optionValue") String optionValue);

    /**
     * 根据字段名和选项文本查找选项
     * @param fieldName 字段名称
     * @param optionText 选项文本
     * @return 选项
     */
    @Select("SELECT * FROM questionnaire_common_options " +
            "WHERE field_name = #{fieldName} AND option_text = #{optionText}")
    QuestionnaireCommonOptions findByFieldAndText(@Param("fieldName") String fieldName, 
                                                 @Param("optionText") String optionText);
}
